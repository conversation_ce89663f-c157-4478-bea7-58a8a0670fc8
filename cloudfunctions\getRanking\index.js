const cloud = require('wx-server-sdk');

cloud.init({
  env: cloud.DYNAMIC_CURRENT_ENV
});

const db = cloud.database();
const _ = db.command;

exports.main = async (event, context) => {
  const { OPENID } = cloud.getWXContext();
  const { type = 'total', limit = 50 } = event;

  try {
    // 获取所有用户的打卡记录
    const punchRecords = await db.collection('punchRecords')
      .where({
        isValidPunch: true
      })
      .get();

    // 按用户分组计算统计数据
    const userStats = {};
    
    punchRecords.data.forEach(record => {
      const openid = record._openid;
      if (!userStats[openid]) {
        userStats[openid] = {
          openid: openid,
          totalDays: 0,
          totalDuration: 0,
          consecutiveDays: 0,
          dailyRecords: {},
          punchedDates: []
        };
      }

      if (!userStats[openid].dailyRecords[record.date]) {
        userStats[openid].dailyRecords[record.date] = 0;
        userStats[openid].punchedDates.push(record.date);
      }
      userStats[openid].dailyRecords[record.date] += record.duration;
      userStats[openid].totalDuration += record.duration;
    });

    // 计算每个用户的统计数据
    const rankings = [];
    
    for (const openid in userStats) {
      const user = userStats[openid];
      user.punchedDates.sort();
      user.totalDays = user.punchedDates.length;

      // 计算连续打卡天数
      if (user.punchedDates.length > 0) {
        user.consecutiveDays = 1;
        const today = new Date();
        const todayStr = `${today.getFullYear()}-${String(today.getMonth() + 1).padStart(2, '0')}-${String(today.getDate()).padStart(2, '0')}`;
        const lastPunchDate = user.punchedDates[user.punchedDates.length - 1];

        // 检查连续性
        if (lastPunchDate === todayStr || isYesterday(lastPunchDate, today)) {
          let currentDate = new Date(lastPunchDate);
          
          for (let i = user.punchedDates.length - 2; i >= 0; i--) {
            const prevDate = new Date(currentDate);
            prevDate.setDate(prevDate.getDate() - 1);
            const prevDateStr = `${prevDate.getFullYear()}-${String(prevDate.getMonth() + 1).padStart(2, '0')}-${String(prevDate.getDate()).padStart(2, '0')}`;

            if (user.punchedDates[i] === prevDateStr) {
              user.consecutiveDays++;
              currentDate = prevDate;
            } else {
              break;
            }
          }
        } else {
          user.consecutiveDays = 0;
        }
      }

      rankings.push({
        openid: openid,
        totalDays: user.totalDays,
        consecutiveDays: user.consecutiveDays,
        totalDuration: user.totalDuration
      });
    }

    // 根据类型排序
    let sortField;
    switch (type) {
      case 'total':
        sortField = 'totalDays';
        break;
      case 'consecutive':
        sortField = 'consecutiveDays';
        break;
      case 'duration':
        sortField = 'totalDuration';
        break;
      default:
        sortField = 'totalDays';
    }

    rankings.sort((a, b) => b[sortField] - a[sortField]);

    // 获取用户信息
    const userInfos = await db.collection('users')
      .where({
        _openid: _.in(rankings.slice(0, limit).map(r => r.openid))
      })
      .get();

    const userInfoMap = {};
    userInfos.data.forEach(user => {
      userInfoMap[user._openid] = user;
    });

    // 构建排行榜结果
    const rankingList = rankings.slice(0, limit).map((ranking, index) => {
      const userInfo = userInfoMap[ranking.openid] || {};
      let value;
      switch (type) {
        case 'total':
          value = ranking.totalDays;
          break;
        case 'consecutive':
          value = ranking.consecutiveDays;
          break;
        case 'duration':
          value = Math.round(ranking.totalDuration / 60); // 转换为分钟
          break;
        default:
          value = ranking.totalDays;
      }

      return {
        rank: index + 1,
        openid: ranking.openid,
        nickname: userInfo.nickName || '匿名用户',
        userId: userInfo.userId || '',
        avatar: userInfo.avatarUrl || '/images/default-avatar.png',
        value: value
      };
    });

    // 查找当前用户排名
    let myRank = null;
    const myRankIndex = rankings.findIndex(r => r.openid === OPENID);
    if (myRankIndex !== -1) {
      const myRanking = rankings[myRankIndex];
      const myUserInfo = userInfoMap[OPENID] || {};
      let myValue;
      switch (type) {
        case 'total':
          myValue = myRanking.totalDays;
          break;
        case 'consecutive':
          myValue = myRanking.consecutiveDays;
          break;
        case 'duration':
          myValue = Math.round(myRanking.totalDuration / 60);
          break;
        default:
          myValue = myRanking.totalDays;
      }

      myRank = {
        rank: myRankIndex + 1,
        openid: OPENID,
        nickname: myUserInfo.nickName || '我的昵称',
        userId: myUserInfo.userId || '',
        avatar: myUserInfo.avatarUrl || '/images/default-avatar.png',
        value: myValue
      };
    }

    return {
      success: true,
      ranking: rankingList,
      myRank: myRank,
      total: rankings.length
    };

  } catch (error) {
    console.error('获取排行榜失败:', error);
    return {
      success: false,
      error: error.message
    };
  }
};

function isYesterday(dateStr, today) {
  const yesterday = new Date(today);
  yesterday.setDate(yesterday.getDate() - 1);
  const yesterdayStr = `${yesterday.getFullYear()}-${String(yesterday.getMonth() + 1).padStart(2, '0')}-${String(yesterday.getDate()).padStart(2, '0')}`;
  return dateStr === yesterdayStr;
}
