import { setupSafeArea, setupLoginStatusListener, cleanupLoginStatusListener, checkLoginStatus, handleLoginSuccess } from '../../utils/auth';
import { db, punchRecordsCollection, UserInfo, PunchRecord } from '../../utils/cloud';

interface CalendarDay {
  day: number | string;
  class: string;
  id: number;
  isPunched?: boolean;
}

Page({
  data: {
    safeAreaTop: 0,
    // 今日练习时长
    todayDuration: '00:00',
    // 今日是否已打卡
    isPunchedToday: false,
    // 总共打卡天数
    totalDays: 0,
    // 连续打卡天数
    consecutiveDays: 0,
    // 累计练习时长（分钟）
    totalMinutes: 0,
    // 日历相关
    currentYear: new Date().getFullYear(),
    currentMonth: new Date().getMonth() + 1,
    calendarDays: [] as CalendarDay[],
    // 登录状态
    isLoggedIn: false,
    openid: '',
    userInfo: null as UserInfo | null,
    // 登录弹窗显示状态
    showLoginPopup: false,
    // 加载状态
    loading: false,
    // 打卡记录
    punchRecords: [] as PunchRecord[]
  },

  // 登录状态变化回调
  loginStatusChangeCallback: null as ((isLoggedIn: boolean, userInfo: UserInfo | null) => void) | null,

  onLoad() {
    setupSafeArea(this);
    setupLoginStatusListener(this, (isLoggedIn: boolean, userInfo: UserInfo | null) => {
      if (isLoggedIn && userInfo) {
        this.loadUserData();
      } else {
        this.clearUserData();
      }
    });
    this.customCheckLoginStatus();
  },

  onShow() {
    this.customCheckLoginStatus();
  },

  onUnload() {
    cleanupLoginStatusListener(this);
  },

  clearUserData() {
    this.setData({
      totalDays: 0,
      consecutiveDays: 0,
      totalMinutes: 0,
      isPunchedToday: false,
      todayDuration: '00:00',
      punchRecords: []
    });
    this.generateCalendar(this.data.currentYear, this.data.currentMonth);
  },

  async customCheckLoginStatus() {
    await checkLoginStatus(this);
    if (this.data.isLoggedIn) {
      await this.loadUserData();
    }
    this.initCalendar();
  },

  showLoginPopup() {
    this.setData({ showLoginPopup: true });
  },

  hideLoginPopup() {
    this.setData({ showLoginPopup: false });
  },

  onLoginSuccess(e: any) {
    const { userInfo, openid } = e.detail;
    handleLoginSuccess(this, userInfo, openid);
    this.loadUserData();
  },

  // 加载用户数据
  async loadUserData() {
    if (!this.data.isLoggedIn || !this.data.userInfo) {
      return;
    }

    try {
      this.setData({ loading: true });
      await this.loadUserStats();
      await this.loadPunchRecords();
      this.checkTodayPunchStatus();
      this.generateCalendar(this.data.currentYear, this.data.currentMonth);
    } catch (error) {
      console.error('加载用户数据失败', error);
      wx.showToast({
        title: '数据加载失败',
        icon: 'none'
      });
    } finally {
      this.setData({ loading: false });
    }
  },

  async loadUserStats() {
    try {
      if (!this.data.userInfo) return;
      const userInfo = this.data.userInfo;
      this.setData({
        totalDays: userInfo.totalDays || 0,
        consecutiveDays: userInfo.consecutiveDays || 0,
        totalMinutes: Math.round((userInfo.totalDuration || 0) / 60)
      });
    } catch (error) {
      console.error('加载用户统计数据失败', error);
    }
  },

  async loadPunchRecords() {
    try {
      if (!this.data.openid) return;

      const startDate = `${this.data.currentYear}-${String(this.data.currentMonth).padStart(2, '0')}-01`;
      const endDate = `${this.data.currentYear}-${String(this.data.currentMonth).padStart(2, '0')}-31`;

      try {
        const result = await punchRecordsCollection
          .where({
            _openid: this.data.openid,
            date: db.command.gte(startDate).and(db.command.lte(endDate))
          })
          .get();

        this.setData({
          punchRecords: result.data as PunchRecord[]
        });
      } catch (dbError: any) {
        if (dbError && dbError.errCode === -502005) {
          console.warn('打卡记录集合不存在，将使用空数组');
          this.setData({
            punchRecords: []
          });
          wx.showToast({
            title: '首次使用，请先完成打卡',
            icon: 'none',
            duration: 2000
          });
        } else {
          throw dbError;
        }
      }
    } catch (error) {
      console.error('加载打卡记录失败', error);
      this.setData({
        punchRecords: []
      });
    }
  },

  checkTodayPunchStatus() {
    const today = new Date();
    const todayStr = `${today.getFullYear()}-${String(today.getMonth() + 1).padStart(2, '0')}-${String(today.getDate()).padStart(2, '0')}`;
    const todayRecord = this.data.punchRecords.find(record => record.date === todayStr);

    if (todayRecord) {
      // duration现在存储的是秒数，直接格式化为"分钟:秒"
      const totalSeconds = todayRecord.duration; // duration现在是秒数
      const minutes = Math.floor(totalSeconds / 60);
      const seconds = totalSeconds % 60;
      const durationStr = `${minutes}分钟:${String(seconds).padStart(2, '0')}秒`;

      // 只有达到10分钟（600秒）才算已打卡
      const isPunchedToday = totalSeconds >= 600;

      this.setData({
        isPunchedToday: isPunchedToday,
        todayDuration: durationStr
      });
    } else {
      this.setData({
        isPunchedToday: false,
        todayDuration: '0分钟:00秒'
      });
    }
  },

  initCalendar() {
    const now = new Date();
    const year = now.getFullYear();
    const month = now.getMonth() + 1;
    this.setData({
      currentYear: year,
      currentMonth: month
    });
    this.generateCalendar(year, month);
  },
  
  generateCalendar(year: number, month: number) {
    const firstDay = new Date(year, month - 1, 1).getDay();
    const totalDays = new Date(year, month, 0).getDate();
    const today = new Date();
    const isCurrentMonth = year === today.getFullYear() && month === today.getMonth() + 1;
    const todayDate = today.getDate();
    const days: CalendarDay[] = [];

    for (let i = 0; i < firstDay; i++) {
      days.push({
        day: '',
        class: 'day empty',
        id: -i
      });
    }

    for (let i = 1; i <= totalDays; i++) {
      const dateStr = `${year}-${String(month).padStart(2, '0')}-${String(i).padStart(2, '0')}`;
      // 只有达到10分钟（600秒）的记录才算已打卡
      const record = this.data.punchRecords.find(record => record.date === dateStr);
      const isPunched = this.data.isLoggedIn && record && record.duration >= 600;
      const isToday = isCurrentMonth && i === todayDate;

      let className = 'day';
      if (isPunched) className += ' punched';
      if (isToday) className += ' today';

      days.push({
        day: i,
        class: className,
        id: i,
        isPunched
      });
    }

    this.setData({
      calendarDays: days
    });
  },
  
  async prevMonth() {
    let { currentYear, currentMonth } = this.data;
    if (currentMonth === 1) {
      currentYear--;
      currentMonth = 12;
    } else {
      currentMonth--;
    }
    this.setData({ currentYear, currentMonth });
    if (this.data.isLoggedIn) {
      await this.loadPunchRecords();
    }
    this.generateCalendar(currentYear, currentMonth);
  },

  async nextMonth() {
    let { currentYear, currentMonth } = this.data;
    if (currentMonth === 12) {
      currentYear++;
      currentMonth = 1;
    } else {
      currentMonth++;
    }
    this.setData({ currentYear, currentMonth });
    if (this.data.isLoggedIn) {
      await this.loadPunchRecords();
    }
    this.generateCalendar(currentYear, currentMonth);
  },

  // 下拉刷新
  onPullDownRefresh() {
    if (this.data.isLoggedIn) {
      this.loadUserData().then(() => {
        wx.stopPullDownRefresh();
      });
    } else {
      wx.stopPullDownRefresh();
    }
  }
})