import { setupSafeArea, setupLoginStatusListener, cleanupLoginStatusListener, checkLoginStatus, handleLoginSuccess, handleUserLogin } from '../../utils/auth';
import { db, userCollection, punchRecordsCollection, UserInfo, PunchRecord, CloudFunctionResult } from '../../utils/cloud';

interface CalendarDay {
  day: number | string;
  class: string;
  id: number;
  isPunched?: boolean;
}

Page({
  data: {
    safeAreaTop: 0,
    // 今日练习时长
    todayDuration: {
      minutes: 0,
      seconds: '00'
    },
    // 今日是否已打卡
    isPunchedToday: false,
    // 总共打卡天数
    totalDays: 0,
    // 连续打卡天数
    consecutiveDays: 0,
    // 累计练习时长（分钟）
    totalMinutes: 0,
    // 日历相关
    currentYear: new Date().getFullYear(),
    currentMonth: new Date().getMonth() + 1,
    calendarDays: [] as CalendarDay[],
    // 登录状态
    isLoggedIn: false,
    openid: '',
    userInfo: null as UserInfo | null,
    // 登录弹窗显示状态
    showLoginPopup: false,
    // 加载状态
    loading: false,
    // 打卡记录
    punchRecords: [] as PunchRecord[]
  },

  // 登录状态变化回调
  loginStatusChangeCallback: null as ((isLoggedIn: boolean, userInfo: UserInfo | null) => void) | null,

  // 防止无限循环的标志位
  isRefreshingFromCloud: false,

  onLoad() {
    setupSafeArea(this);
    setupLoginStatusListener(this, (isLoggedIn: boolean, userInfo: UserInfo | null) => {
      if (isLoggedIn && userInfo) {
        // 登录状态变化时不刷新云端数据，避免无限循环
        this.loadUserData(false);
      } else {
        this.clearUserData();
      }
    });
    this.customCheckLoginStatus();
  },

  onShow() {
    this.customCheckLoginStatus();
  },

  onUnload() {
    cleanupLoginStatusListener(this);
  },

  clearUserData() {
    this.setData({
      totalDays: 0,
      consecutiveDays: 0,
      totalMinutes: 0,
      isPunchedToday: false,
      todayDuration: {
        minutes: 0,
        seconds: '00'
      },
      punchRecords: []
    });
    this.generateCalendar(this.data.currentYear, this.data.currentMonth);
  },

  async customCheckLoginStatus() {
    await checkLoginStatus(this);
    if (this.data.isLoggedIn) {
      // 检查登录状态时不刷新云端数据，避免无限循环
      await this.loadUserData(false);
    }
    this.initCalendar();
  },

  async showLoginPopup() {
    await handleUserLogin(this);
  },

  hideLoginPopup() {
    this.setData({ showLoginPopup: false });
  },

  onLoginSuccess(e: any) {
    const { userInfo, openid } = e.detail;
    handleLoginSuccess(this, userInfo, openid);
    // 登录成功后刷新云端数据
    this.loadUserData(true);
  },

  // 加载用户数据
  async loadUserData(refreshFromCloud: boolean = false) {
    if (!this.data.isLoggedIn || !this.data.userInfo) {
      return;
    }

    try {
      this.setData({ loading: true });

      // 只在明确需要时才从云端刷新数据
      if (refreshFromCloud) {
        await this.refreshUserStatsFromCloud();
      }

      await this.loadUserStats();
      await this.loadPunchRecords();
      this.checkTodayPunchStatus();
      this.generateCalendar(this.data.currentYear, this.data.currentMonth);
    } catch (error) {
      console.error('加载用户数据失败', error);
      wx.showToast({
        title: '数据加载失败',
        icon: 'none'
      });
    } finally {
      this.setData({ loading: false });
    }
  },

  async loadUserStats() {
    try {
      if (!this.data.openid) return;

      console.log('调用云函数获取用户统计数据');

      // 使用新的云函数获取统计数据
      const result = await wx.cloud.callFunction({
        name: 'getUserStats',
        data: {
          useCache: true
        }
      }) as CloudFunctionResult;

      if (result.result && (result.result as any).success) {
        const stats = result.result as any;
        console.log('获取用户统计数据成功:', stats);

        this.setData({
          totalDays: stats.totalDays || 0,
          consecutiveDays: stats.consecutiveDays || 0,
          totalMinutes: Math.round((stats.totalDuration || 0) / 60)
        });
      } else {
        console.error('获取用户统计数据失败:', (result.result as any)?.error);
      }
    } catch (error) {
      console.error('加载用户统计数据失败', error);
    }
  },

  // 从云端刷新用户统计数据
  async refreshUserStatsFromCloud() {
    try {
      if (!this.data.openid || this.isRefreshingFromCloud) return;

      this.isRefreshingFromCloud = true;

      console.log('从云端刷新用户统计数据');

      // 使用新的云函数获取最新统计数据（不使用缓存）
      const result = await wx.cloud.callFunction({
        name: 'getUserStats',
        data: {
          useCache: false
        }
      }) as CloudFunctionResult;

      if (result.result && (result.result as any).success) {
        const stats = result.result as any;
        console.log('从云端获取统计数据成功:', stats);

        // 更新本地用户信息
        const updatedUserInfo: UserInfo = {
          avatarUrl: this.data.userInfo?.avatarUrl || '',
          nickName: this.data.userInfo?.nickName || '',
          signature: this.data.userInfo?.signature || '坚持，是一种力量',
          userId: this.data.userInfo?.userId || '',
          totalDays: stats.totalDays || 0,
          consecutiveDays: stats.consecutiveDays || 0,
          totalDuration: stats.totalDuration || 0,
          gender: this.data.userInfo?.gender,
          age: this.data.userInfo?.age
        };

        this.setData({
          userInfo: updatedUserInfo
        });

        // 同步更新本地存储
        wx.setStorageSync('userInfo', updatedUserInfo);

        console.log('用户统计数据已从云端刷新:', updatedUserInfo);
      } else {
        console.error('从云端获取统计数据失败:', (result.result as any)?.error);
      }
    } catch (error) {
      console.error('从云端刷新用户统计数据失败', error);
    } finally {
      this.isRefreshingFromCloud = false;
    }
  },

  async loadPunchRecords() {
    try {
      if (!this.data.openid) return;

      const startDate = `${this.data.currentYear}-${String(this.data.currentMonth).padStart(2, '0')}-01`;
      const endDate = `${this.data.currentYear}-${String(this.data.currentMonth).padStart(2, '0')}-31`;

      try {
        const result = await punchRecordsCollection
          .where({
            _openid: this.data.openid,
            date: db.command.gte(startDate).and(db.command.lte(endDate))
          })
          .get();

        this.setData({
          punchRecords: result.data as PunchRecord[]
        });
      } catch (dbError: any) {
        if (dbError && dbError.errCode === -502005) {
          console.warn('打卡记录集合不存在，将使用空数组');
          this.setData({
            punchRecords: []
          });
          wx.showToast({
            title: '首次使用，请先完成打卡',
            icon: 'none',
            duration: 2000
          });
        } else {
          throw dbError;
        }
      }
    } catch (error) {
      console.error('加载打卡记录失败', error);
      this.setData({
        punchRecords: []
      });
    }
  },

  checkTodayPunchStatus() {
    const today = new Date();
    const todayStr = `${today.getFullYear()}-${String(today.getMonth() + 1).padStart(2, '0')}-${String(today.getDate()).padStart(2, '0')}`;
    const todayRecord = this.data.punchRecords.find(record => record.date === todayStr);

    if (todayRecord) {
      // duration现在存储的是秒数，格式化为优化的显示格式
      const totalSeconds = todayRecord.duration; // duration现在是秒数
      const minutes = Math.floor(totalSeconds / 60);
      const seconds = totalSeconds % 60;

      // 只有达到10分钟（600秒）才算已打卡
      const isPunchedToday = totalSeconds >= 600;

      this.setData({
        isPunchedToday: isPunchedToday,
        todayDuration: {
          minutes: minutes,
          seconds: String(seconds).padStart(2, '0')
        }
      });
    } else {
      this.setData({
        isPunchedToday: false,
        todayDuration: {
          minutes: 0,
          seconds: '00'
        }
      });
    }
  },

  // 重新计算用户统计数据（调试用）
  async recalculateUserStats() {
    try {
      if (!this.data.openid) {
        wx.showToast({
          title: '请先登录',
          icon: 'none'
        });
        return;
      }

      wx.showLoading({
        title: '重新计算中...'
      });

      console.log('调用云函数重新计算统计数据');

      // 使用新的云函数重新计算统计数据
      const result = await wx.cloud.callFunction({
        name: 'recalculateStats',
        data: {
          openid: this.data.openid
        }
      }) as CloudFunctionResult;

      if (result.result && (result.result as any).success) {
        console.log('重新计算统计数据成功:', result.result);

        // 刷新页面数据
        await this.refreshUserStatsFromCloud();
        await this.loadUserData();

        wx.hideLoading();
        wx.showToast({
          title: '重新计算完成',
          icon: 'success'
        });
      } else {
        wx.hideLoading();
        console.error('重新计算统计数据失败:', (result.result as any)?.error);
        wx.showToast({
          title: '计算失败',
          icon: 'none'
        });
      }
    } catch (error) {
      wx.hideLoading();
      console.error('重新计算统计数据失败', error);
      wx.showToast({
        title: '计算失败',
        icon: 'none'
      });
    }
  },

  initCalendar() {
    const now = new Date();
    const year = now.getFullYear();
    const month = now.getMonth() + 1;
    this.setData({
      currentYear: year,
      currentMonth: month
    });
    this.generateCalendar(year, month);
  },
  
  generateCalendar(year: number, month: number) {
    const firstDay = new Date(year, month - 1, 1).getDay();
    const totalDays = new Date(year, month, 0).getDate();
    const today = new Date();
    const isCurrentMonth = year === today.getFullYear() && month === today.getMonth() + 1;
    const todayDate = today.getDate();
    const days: CalendarDay[] = [];

    for (let i = 0; i < firstDay; i++) {
      days.push({
        day: '',
        class: 'day empty',
        id: -i
      });
    }

    for (let i = 1; i <= totalDays; i++) {
      const dateStr = `${year}-${String(month).padStart(2, '0')}-${String(i).padStart(2, '0')}`;
      // 只有达到10分钟（600秒）的记录才算已打卡
      const record = this.data.punchRecords.find(record => record.date === dateStr);
      const isPunched = this.data.isLoggedIn && record && record.duration >= 600;
      const isToday = isCurrentMonth && i === todayDate;

      let className = 'day';
      if (isPunched) className += ' punched';
      if (isToday) className += ' today';

      days.push({
        day: i,
        class: className,
        id: i,
        isPunched
      });
    }

    this.setData({
      calendarDays: days
    });
  },
  
  async prevMonth() {
    let { currentYear, currentMonth } = this.data;
    if (currentMonth === 1) {
      currentYear--;
      currentMonth = 12;
    } else {
      currentMonth--;
    }
    this.setData({ currentYear, currentMonth });
    if (this.data.isLoggedIn) {
      await this.loadPunchRecords();
    }
    this.generateCalendar(currentYear, currentMonth);
  },

  async nextMonth() {
    let { currentYear, currentMonth } = this.data;
    if (currentMonth === 12) {
      currentYear++;
      currentMonth = 1;
    } else {
      currentMonth++;
    }
    this.setData({ currentYear, currentMonth });
    if (this.data.isLoggedIn) {
      await this.loadPunchRecords();
    }
    this.generateCalendar(currentYear, currentMonth);
  },

  // 下拉刷新
  onPullDownRefresh() {
    if (this.data.isLoggedIn) {
      // 下拉刷新时从云端获取最新数据
      this.loadUserData(true).then(() => {
        wx.stopPullDownRefresh();
      });
    } else {
      wx.stopPullDownRefresh();
    }
  }
})