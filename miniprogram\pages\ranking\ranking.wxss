/* 排行榜页面样式 */
.container {
  height: 100vh;
  display: flex;
  flex-direction: column;
  box-sizing: border-box;
}

.header {
  padding-bottom: 0;
}

.content {
  flex: 1;
  overflow-y: auto;
  -webkit-overflow-scrolling: touch;
}

.tab-bar {
  display: flex;
  border-bottom: 1rpx solid #E5E7EB;
}

.tab {
  flex: 1;
  padding: 24rpx 0;
  font-size: 28rpx;
  font-weight: 600;
  text-align: center;
  color: #6B7280;
}

.tab.active {
  color: #92400E;
  border-bottom: 4rpx solid #92400E;
}

/* 排行榜提示文本 */
.ranking-note {
  font-size: 24rpx;
  color: #9CA3AF;
  text-align: right;
  padding: 10rpx 0;
  margin-top: 4rpx;
}

.space-y-2 > view:not(:first-child) {
  margin-top: 16rpx;
}

.rank-item {
  display: flex;
  align-items: center;
  background-color: white;
  padding: 24rpx;
  border-radius: 16rpx;
  position: relative;
}

.top-rank {
  background-color: white;
  box-shadow: 0 2rpx 4rpx rgba(0, 0, 0, 0.05);
}

.rank-number {
  width: 80rpx;
  text-align: center;
  font-weight: bold;
  font-size: 32rpx;
  color: #6B7280;
}

.rank-1 {
  color: #F59E0B;
  font-size: 40rpx;
}

.rank-2 {
  color: #9CA3AF;
  font-size: 36rpx;
}

.rank-3 {
  color: #B45309;
  font-size: 32rpx;
}

.avatar {
  border-radius: 100%;
  margin: 0 24rpx;
}

.large {
  width: 96rpx;
  height: 96rpx;
}

.small {
  width: 80rpx;
  height: 80rpx;
}

.user-info {
  flex: 1;
}

.nickname {
  font-weight: 600;
  color: #1F2937;
  font-size: 28rpx;
}

.user-id {
  font-size: 24rpx;
  color: #9CA3AF;
  margin-top: 4rpx;
}

.rank-value {
  font-weight: bold;
  color: #1F2937;
}

.my-rank {
  position: fixed;
  bottom: 140rpx;
  left: 0;
  right: 0;
  padding: 32rpx;
  background-color: rgba(255, 255, 255, 0.8);
  backdrop-filter: blur(10rpx);
}

.login-prompt {
  display: flex;
  justify-content: center;
  align-items: center;
  padding: 24rpx;
}

.login-btn {
  background-color: #92400E;
  color: #FFFFFF;
  border: none;
  border-radius: 16rpx;
  padding: 24rpx 48rpx;
  font-size: 28rpx;
  font-weight: 600;
  display: flex;
  align-items: center;
  justify-content: center;
  min-width: 200rpx;
}

.login-btn::after {
  border: none;
}

.login-btn:active {
  background-color: #7C2D12;
  transform: scale(0.98);
}

/* 加载状态 */
.loading-container {
  display: flex;
  justify-content: center;
  align-items: center;
  padding: 80rpx 0;
}

.loading-text {
  color: #6B7280;
  font-size: 28rpx;
}

/* 空状态 */
.empty-state {
  display: flex;
  flex-direction: column;
  align-items: center;
  padding: 120rpx 48rpx;
}

.empty-text {
  color: #6B7280;
  font-size: 32rpx;
  font-weight: 600;
  margin-bottom: 16rpx;
}

.empty-desc {
  color: #9CA3AF;
  font-size: 24rpx;
}

.my-rank .rank-item {
  background-color: #FEF3C7;
  border: 1rpx solid #F59E0B;
}

.my-nickname {
  color: #92400E;
  font-weight: bold;
}

.my-value {
  color: #92400E;
  font-weight: bold;
}