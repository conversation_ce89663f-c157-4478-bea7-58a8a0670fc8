<!--我的页面-->
<view class="container">
  <!-- 胶囊按钮安全区域 -->
  <view class="capsule-safe-area" style="height: {{safeAreaTop}}px;"></view>

  <!-- 页面标题 -->
  <view class="header p-6">
    <view class="font-bold text-dark" style="font-size: 48rpx;">我的</view>
  </view>

  <!-- 个人资料区域 -->
  <view class="profile-section">
    <!-- 头像可点击 -->
    <image class="avatar" src="{{userInfo.avatarUrl || '/images/default-avatar.png'}}" bindtap="{{hasUserInfo ? 'editProfile' : 'handleUserLogin'}}"></image>
    <!-- 已登录显示用户信息 -->
    <view class="user-info" wx:if="{{hasUserInfo}}" bindtap="editProfile">
      <view class="nickname">{{userInfo.nickName}}</view>
      <view class="user-id" wx:if="{{userInfo.userId}}">ID: {{userInfo.userId}}</view>
      <view class="signature">{{userInfo.signature}}</view>
    </view>
    <!-- 未登录不显示任何文本 -->
    <view class="user-info" wx:else>
    </view>
    <!-- 微信登录按钮 -->
    <button wx:if="{{!hasUserInfo}}" class="login-btn" bindtap="handleUserLogin">微信登录</button>
    <!-- 编辑资料图标 -->
    <view wx:else class="edit-profile" bindtap="editProfile">
      <image src="/images/jinru.png" mode="aspectFit" class="edit-arrow"></image>
    </view>
  </view>

  <!-- 菜单列表 -->
  <view class="menu-card">
    <!-- 加入社群 -->
    <navigator url="../community/community" class="menu-item">
      <view class="menu-icon community-icon"></view>
      <text class="menu-text">加入社群</text>
      <image src="/images/jinru.png" mode="aspectFit" class="menu-arrow"></image>
    </navigator>

    <!-- 分享好友 -->
    <navigator class="menu-item">
      <view class="menu-icon share-icon"></view>
      <text class="menu-text">分享好友</text>
      <image src="/images/jinru.png" mode="aspectFit" class="menu-arrow"></image>
    </navigator>

    <!-- 联系客服 -->
    <navigator class="menu-item">
      <view class="menu-icon service-icon"></view>
      <text class="menu-text">联系客服</text>
      <image src="/images/jinru.png" mode="aspectFit" class="menu-arrow"></image>
    </navigator>
  </view>

  <!-- 登录弹窗 -->
  <login-popup show="{{showLoginPopup}}" bind:close="hideLoginPopup" bind:save="onLoginSuccess"></login-popup>
</view>