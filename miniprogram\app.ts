App<IAppOption>({
  globalData: {
    isLoggedIn: false,
    userInfo: null,
    openid: '',
    loginCallbacks: []
  },

  onLaunch() {
    const logs = wx.getStorageSync('logs') || []
    logs.unshift(Date.now())
    wx.setStorageSync('logs', logs)

    wx.login({
      success: res => {
        console.log(res.code)
      },
    })
  },

  setGlobalLoginStatus(isLoggedIn: boolean, userInfo: UserInfo | null = null, openid: string = '') {
    this.globalData.isLoggedIn = isLoggedIn;
    this.globalData.userInfo = userInfo;
    this.globalData.openid = openid;
    this.globalData.loginCallbacks.forEach(callback => {
      callback(isLoggedIn, userInfo);
    });
  },

  onLoginStatusChange(callback: (isLoggedIn: boolean, userInfo: UserInfo | null) => void) {
    this.globalData.loginCallbacks.push(callback);
  },

  offLoginStatusChange(callback: (isLoggedIn: boolean, userInfo: UserInfo | null) => void) {
    const index = this.globalData.loginCallbacks.indexOf(callback);
    if (index > -1) {
      this.globalData.loginCallbacks.splice(index, 1);
    }
  }
})