// 补充微信小程序API的类型定义
declare namespace WechatMiniprogram {
  interface Wx {
    getWindowInfo(): WindowInfo;
    getDeviceInfo(): DeviceInfo;
    getAppBaseInfo(): AppBaseInfo;
    getSystemSetting(): SystemSetting;
    getAppAuthorizeSetting(): AppAuthorizeSetting;
  }

  interface WindowInfo {
    /** 设备像素比 */
    pixelRatio: number;
    /** 屏幕宽度，单位px */
    screenWidth: number;
    /** 屏幕高度，单位px */
    screenHeight: number;
    /** 可使用窗口宽度，单位px */
    windowWidth: number;
    /** 可使用窗口高度，单位px */
    windowHeight: number;
    /** 状态栏的高度，单位px */
    statusBarHeight: number;
    /** 在竖屏正方向下的安全区域 */
    safeArea: SafeArea;
    /** 窗口上边缘的y值 */
    screenTop: number;
  }

  interface SafeArea {
    /** 安全区域左上角横坐标 */
    left: number;
    /** 安全区域左上角纵坐标 */
    top: number;
    /** 安全区域右下角横坐标 */
    right: number;
    /** 安全区域右下角纵坐标 */
    bottom: number;
    /** 安全区域的宽度，单位逻辑像素 */
    width: number;
    /** 安全区域的高度，单位逻辑像素 */
    height: number;
  }

  interface DeviceInfo {
    /** 设备品牌 */
    brand: string;
    /** 设备型号 */
    model: string;
    /** 设备像素比 */
    pixelRatio: number;
    /** 屏幕宽度，单位px */
    screenWidth: number;
    /** 屏幕高度，单位px */
    screenHeight: number;
    /** 可使用窗口宽度，单位px */
    windowWidth: number;
    /** 可使用窗口高度，单位px */
    windowHeight: number;
    /** 状态栏的高度，单位px */
    statusBarHeight: number;
    /** 微信设置的语言 */
    language: string;
    /** 微信版本号 */
    version: string;
    /** 操作系统及版本 */
    system: string;
    /** 客户端平台 */
    platform: string;
    /** 用户字体大小（单位px）。以微信客户端「我-设置-通用-字体大小」中的设置为准 */
    fontSizeSetting: number;
    /** 客户端基础库版本 */
    SDKVersion: string;
    /** 设备性能等级（仅Android小游戏）。取值为：-2 或 0（该设备无法运行小游戏），-1（性能未知），>=1（设备性能值，该值越高，设备性能越好，目前最高不到50） */
    benchmarkLevel: number;
    /** 允许微信使用相册的开关（仅 iOS 有效） */
    albumAuthorized: boolean;
    /** 允许微信使用摄像头的开关 */
    cameraAuthorized: boolean;
    /** 允许微信使用定位的开关 */
    locationAuthorized: boolean;
    /** 允许微信使用麦克风的开关 */
    microphoneAuthorized: boolean;
    /** 允许微信通知的开关 */
    notificationAuthorized: boolean;
    /** 允许微信通知带有提醒的开关（仅 iOS 有效） */
    notificationAlertAuthorized: boolean;
    /** 允许微信通知带有标记的开关（仅 iOS 有效） */
    notificationBadgeAuthorized: boolean;
    /** 允许微信通知带有声音的开关（仅 iOS 有效） */
    notificationSoundAuthorized: boolean;
    /** 蓝牙的系统开关 */
    bluetoothEnabled: boolean;
    /** 地理位置的系统开关 */
    locationEnabled: boolean;
    /** Wi-Fi 的系统开关 */
    wifiEnabled: boolean;
  }

  interface AppBaseInfo {
    /** 小程序当前运行环境 */
    env: string;
    /** 小程序版本 */
    version: string;
    /** 小程序基础库版本 */
    SDKVersion: string;
    /** 宿主平台，如：'ios'、'android'、'windows'、'mac'、'devtools'等 */
    platform: string;
    /** 宿主平台版本号 */
    platformVersion: string;
    /** 宿主平台名称 */
    platformName: string;
    /** 宿主平台语言 */
    language: string;
    /** 宿主平台主题 */
    theme: string;
  }

  interface SystemSetting {
    /** 蓝牙的系统开关 */
    bluetoothEnabled: boolean;
    /** 地理位置的系统开关 */
    locationEnabled: boolean;
    /** Wi-Fi 的系统开关 */
    wifiEnabled: boolean;
    /** 设备方向 */
    deviceOrientation: string;
  }

  interface AppAuthorizeSetting {
    /** 允许微信使用相册的开关（仅 iOS 有效） */
    albumAuthorized: boolean;
    /** 允许微信使用蓝牙的开关（仅 iOS 有效） */
    bluetoothAuthorized: boolean;
    /** 允许微信使用摄像头的开关 */
    cameraAuthorized: boolean;
    /** 允许微信使用定位的开关 */
    locationAuthorized: boolean;
    /** 定位准确度。true 表示模糊定位，false 表示精确定位（仅 iOS 有效） */
    locationReducedAccuracy: boolean;
    /** 允许微信使用麦克风的开关 */
    microphoneAuthorized: boolean;
    /** 允许微信通知的开关 */
    notificationAuthorized: boolean;
    /** 允许微信通知带有提醒的开关（仅 iOS 有效） */
    notificationAlertAuthorized: boolean;
    /** 允许微信通知带有标记的开关（仅 iOS 有效） */
    notificationBadgeAuthorized: boolean;
    /** 允许微信通知带有声音的开关（仅 iOS 有效） */
    notificationSoundAuthorized: boolean;
  }
} 