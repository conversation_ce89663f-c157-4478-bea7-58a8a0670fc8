# 一起八段锦

<div align="center">
  <img src="miniprogram/images/home-active.png" alt="项目Logo" width="120">
  <h3>每日一练，身心康健</h3>
  <p>
    <img src="https://img.shields.io/badge/微信小程序-原生框架-brightgreen" alt="微信小程序">
    <img src="https://img.shields.io/badge/TypeScript-5.8.3-blue" alt="TypeScript">
    <img src="https://img.shields.io/badge/云开发-腾讯云-orange" alt="云开发">
    <img src="https://img.shields.io/badge/License-MIT-yellow" alt="License">
  </p>
</div>

## 📝 项目简介

"一起八段锦"是一款以八段锦日常跟练为核心的微信小程序，旨在帮助用户养成每日锻炼的健康习惯。八段锦作为中国传统养生功法，简单易学、效果显著，适合各年龄段人群练习。

本小程序通过提供多版本的教学视频、智能打卡机制、数据统计和社区排行榜等功能，解决用户在独自练习时缺乏指导、难以坚持、缺少反馈的问题，营造一个积极、健康的线上养生社区。

## ✨ 核心功能

### 🎬 视频跟练系统
- **多版本教学视频**：提供体大刘晓蕾版（竖屏）和广卫青年双人版（横屏）等多种教学视频
- **智能播放控制**：根据视频方向自动切换全屏模式，提供最佳观看体验
- **云存储支持**：视频文件存储在腾讯云，支持临时URL获取和高效播放
- **播放进度追踪**：实时记录用户观看时长，为打卡功能提供数据支持

### 📅 智能打卡系统
- **自动打卡机制**：观看视频超过10分钟自动完成当日打卡，无需手动操作
- **多维度统计**：记录总打卡天数、连续打卡天数和累计练习时长
- **日历可视化**：直观展示历史打卡记录，支持月份切换查看
- **数据持久化**：打卡记录存储在云数据库，支持跨设备同步

### 🏆 社区排行榜
- **多维度排名**：总打卡天数榜、连续打卡天数榜、总练习时长榜
- **实时更新**：排行榜数据实时从云数据库获取，确保数据准确性
- **个人排名显示**：用户可查看自己在各个榜单中的排名位置
- **社区激励**：通过排名机制激发用户持续练习的动力

### 👤 用户管理系统
- **完善的用户档案**：支持头像、昵称、个性签名、性别、年龄等信息管理
- **8位序列用户ID**：自动生成00000001开始的8位用户ID，便于用户识别
- **隐私保护**：集成微信官方隐私授权弹窗，支持手动输入备选方案
- **多端同步**：用户信息存储在云数据库，支持多设备登录同步

### 🔐 登录认证系统
- **微信授权登录**：支持微信头像昵称快速登录
- **手动注册备选**：用户拒绝授权时可手动上传头像和填写昵称
- **全局状态管理**：统一的登录状态管理，支持跨页面状态同步
- **自动登录恢复**：应用重启后自动恢复登录状态

## 🛠️ 技术架构

### 前端技术栈
- **开发框架**：微信小程序原生框架
- **编程语言**：TypeScript 5.8.3
- **UI框架**：自定义组件 + 原生WXML/WXSS
- **状态管理**：全局状态管理 + 本地存储
- **样式设计**：中式禅意风格，响应式布局

### 后端技术栈
- **云开发平台**：腾讯云微信云开发
- **云环境ID**：cloud1-5go7er9y38b6f1bd
- **数据库**：云数据库（NoSQL）
- **云存储**：腾讯云对象存储（视频文件）
- **云函数**：Node.js运行时

### 数据库设计
```javascript
// 用户信息表 (users)
{
  _id: "自动生成",
  _openid: "用户openid",
  userId: "8位序列ID（00000001开始）",
  avatarUrl: "头像URL",
  nickName: "用户昵称",
  signature: "个性签名",
  gender: "性别（可选）",
  age: "年龄（可选）",
  totalDays: "总打卡天数",
  consecutiveDays: "连续打卡天数",
  totalDuration: "总练习时长（分钟）",
  createdAt: "创建时间",
  updatedAt: "更新时间"
}

// 打卡记录表 (punchRecords)
{
  _id: "自动生成",
  _openid: "用户openid",
  date: "打卡日期（YYYY-MM-DD）",
  duration: "练习时长（分钟）",
  createdAt: "创建时间"
}
```

## 📂 项目结构

```
yqbdj/
├── miniprogram/                    # 小程序主目录
│   ├── app.ts                     # 应用程序入口文件
│   ├── app.json                   # 全局配置文件
│   ├── app.wxss                   # 全局样式文件
│   ├── images/                    # 图片资源目录
│   │   ├── home.png              # 首页图标
│   │   ├── daka.png              # 打卡图标
│   │   ├── ranking.png           # 排行榜图标
│   │   ├── me.png                # 个人中心图标
│   │   ├── video-cover1.jpg      # 视频封面1
│   │   └── video-cover2.jpg      # 视频封面2
│   ├── pages/                     # 页面目录
│   │   ├── index/                # 首页（视频列表）
│   │   │   ├── index.ts          # 页面逻辑
│   │   │   ├── index.wxml        # 页面结构
│   │   │   ├── index.wxss        # 页面样式
│   │   │   └── index.json        # 页面配置
│   │   ├── daka/                 # 打卡页面
│   │   ├── ranking/              # 排行榜页面
│   │   ├── me/                   # 个人中心页面
│   │   ├── profile/              # 个人资料编辑页面
│   │   ├── video/                # 视频播放页面
│   │   └── logs/                 # 日志页面
│   ├── components/               # 自定义组件
│   │   └── login-popup/          # 登录弹窗组件
│   └── utils/                    # 工具函数目录
│       └── util.ts               # 通用工具函数
├── cloudfunctions/               # 云函数目录
│   └── getOpenid/               # 获取用户openid云函数
│       ├── index.js             # 云函数入口
│       └── package.json         # 云函数依赖
├── cloudbaserc/                 # 云开发配置目录
├── phototype/                   # 高保真原型文件
│   ├── index.html              # 原型首页
│   ├── home.html               # 首页原型
│   ├── daka.html               # 打卡页原型
│   ├── ranking.html            # 排行榜原型
│   └── me.html                 # 个人中心原型
├── typings/                     # TypeScript类型定义
├── package.json                 # 项目依赖配置
├── tsconfig.json               # TypeScript配置
├── project.config.json         # 小程序项目配置
└── README.md                   # 项目说明文档
```

## 🚀 快速开始

### 环境要求
- **微信开发者工具**：[下载地址](https://developers.weixin.qq.com/miniprogram/dev/devtools/download.html)
- **微信小程序账号**：需要注册并获取AppID
- **腾讯云账号**：用于云开发环境（可选，已配置测试环境）
- **Node.js**：版本 >= 14.0.0（用于云函数开发）

### 安装步骤

1. **克隆项目**
   ```bash
   git clone [仓库地址]
   cd yqbdj
   ```

2. **安装依赖**
   ```bash
   npm install
   ```

3. **配置小程序**
   - 打开微信开发者工具
   - 选择"导入项目"
   - 选择项目根目录 `yqbdj`
   - 填入你的AppID（测试可使用测试号）

4. **配置云开发环境**
   - 在微信开发者工具中开通云开发
   - 创建云环境或使用现有环境：`cloud1-5go7er9y38b6f1bd`
   - 上传云函数：右键 `cloudfunctions/getOpenid` → 上传并部署

5. **初始化数据库**
   - 在云开发控制台创建以下集合：
     - `users`：用户信息表
     - `punchRecords`：打卡记录表
   - 为性能优化，建议创建以下索引：
     - `users` 表：`totalDays`、`consecutiveDays`、`totalDuration` 降序索引
     - `punchRecords` 表：`date` 升序索引

### 开发调试

1. **本地调试**
   ```bash
   # 在微信开发者工具中
   - 点击"编译"按钮
   - 在模拟器中预览效果
   - 使用调试器查看日志
   ```

2. **真机测试**
   ```bash
   # 在微信开发者工具中
   - 点击"预览"按钮
   - 扫描二维码在真机上测试
   - 验证云开发功能是否正常
   ```

3. **云函数调试**
   ```bash
   # 在云开发控制台
   - 进入云函数管理
   - 选择对应函数进行在线调试
   - 查看函数执行日志
   ```

## 🎯 功能演示

### 页面截图展示
项目包含高保真原型演示，可通过以下方式查看：

1. **在线预览**：打开 `phototype/index.html` 查看完整原型
2. **页面展示**：
   - 首页：视频列表和开始练习
   - 打卡页：日历视图和统计数据
   - 排行榜：多维度排名展示
   - 个人中心：用户信息管理

### 核心流程
1. **用户注册/登录** → 选择头像昵称 → 完成个人信息
2. **选择视频** → 开始练习 → 自动记录时长
3. **达到10分钟** → 自动打卡 → 更新统计数据
4. **查看排行榜** → 了解排名 → 激励持续练习

## 📊 项目特色

### 技术亮点
- **TypeScript全栈开发**：前端页面和云函数均使用TypeScript
- **云原生架构**：基于腾讯云微信云开发，无需服务器运维
- **智能打卡算法**：基于视频观看时长的自动打卡机制
- **性能优化**：数据库索引优化、分页查询、缓存策略
- **用户体验**：响应式设计、加载状态、错误处理

### 业务创新
- **多维度激励**：总天数、连续天数、总时长三重排行榜
- **隐私友好**：支持官方授权和手动输入双重方案
- **社区化运营**：通过排行榜营造良性竞争氛围
- **数据驱动**：完整的用户行为数据收集和分析

## 🔧 开发指南

### 代码规范
- 使用TypeScript严格模式
- 遵循ESLint代码规范
- 组件化开发，提高代码复用性
- 统一的错误处理和日志记录

### 部署说明
1. **测试环境**：使用云开发测试环境进行功能验证
2. **生产环境**：配置独立的云环境ID
3. **版本发布**：通过微信开发者工具提交审核

### 性能优化建议
- 为数据库字段创建合适的索引
- 使用分页查询避免大数据量传输
- 图片资源使用CDN加速
- 合理使用本地缓存减少网络请求

## 📄 相关文档

- **高保真原型**：`phototype/index.html` - 完整的UI设计展示
- **API文档**：云函数接口说明和调用示例
- **数据库设计**：表结构和字段说明
- **部署指南**：生产环境配置和发布流程

## 🤝 参与贡献

我们欢迎所有形式的贡献，包括但不限于：

### 贡献方式
1. **代码贡献**：修复bug、新增功能、性能优化
2. **文档完善**：改进文档、添加示例、翻译内容
3. **问题反馈**：报告bug、提出改进建议
4. **功能建议**：提出新功能需求和设计方案

### 贡献流程
1. Fork 本仓库到你的GitHub账号
2. 创建特性分支 (`git checkout -b feature/amazing-feature`)
3. 提交你的更改 (`git commit -m 'Add some amazing feature'`)
4. 推送到分支 (`git push origin feature/amazing-feature`)
5. 创建 Pull Request 并描述你的更改

### 开发规范
- 提交前请运行测试确保功能正常
- 遵循现有的代码风格和命名规范
- 为新功能添加相应的文档说明
- 重大更改请先创建Issue讨论

## 📱 体验小程序

<div align="center">
  <img src="miniprogram/images/qrcode.jpg" alt="小程序码" width="200">
  <p><strong>扫描上方小程序码，立即体验"一起八段锦"</strong></p>
  <p>或在微信中搜索"一起八段锦"小程序</p>
</div>

## 📞 联系我们

- **项目维护者**：[GitHub用户名]
- **问题反馈**：[GitHub Issues](https://github.com/username/yqbdj/issues)
- **邮箱联系**：<EMAIL>
- **微信群**：扫描小程序码加入用户交流群

## 📝 开源协议

本项目基于 [MIT License](LICENSE) 开源协议发布。

```
MIT License

Copyright (c) 2024 一起八段锦项目组

Permission is hereby granted, free of charge, to any person obtaining a copy
of this software and associated documentation files (the "Software"), to deal
in the Software without restriction, including without limitation the rights
to use, copy, modify, merge, publish, distribute, sublicense, and/or sell
copies of the Software, and to permit persons to whom the Software is
furnished to do so, subject to the following conditions:

The above copyright notice and this permission notice shall be included in all
copies or substantial portions of the Software.

THE SOFTWARE IS PROVIDED "AS IS", WITHOUT WARRANTY OF ANY KIND, EXPRESS OR
IMPLIED, INCLUDING BUT NOT LIMITED TO THE WARRANTIES OF MERCHANTABILITY,
FITNESS FOR A PARTICULAR PURPOSE AND NONINFRINGEMENT. IN NO EVENT SHALL THE
AUTHORS OR COPYRIGHT HOLDERS BE LIABLE FOR ANY CLAIM, DAMAGES OR OTHER
LIABILITY, WHETHER IN AN ACTION OF CONTRACT, TORT OR OTHERWISE, ARISING FROM,
OUT OF OR IN CONNECTION WITH THE SOFTWARE OR THE USE OR OTHER DEALINGS IN THE
SOFTWARE.
```

---

<div align="center">
  <p>⭐ 如果这个项目对你有帮助，请给我们一个Star！</p>
  <p>🙏 感谢所有贡献者的支持和参与！</p>
</div>