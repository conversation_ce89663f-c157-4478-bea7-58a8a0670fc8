const cloud = require('wx-server-sdk');

cloud.init({
  env: cloud.DYNAMIC_CURRENT_ENV
});

const db = cloud.database();
const _ = db.command;

exports.main = async (event, context) => {
  const { step = 'all' } = event;

  try {
    let results = {};

    if (step === 'all' || step === 'punchRecords') {
      results.punchRecords = await migratePunchRecords();
    }

    if (step === 'all' || step === 'userStats') {
      results.userStats = await createUserStatsCollection();
    }

    if (step === 'all' || step === 'cleanUsers') {
      results.cleanUsers = await cleanUsersCollection();
    }

    return {
      success: true,
      results: results
    };

  } catch (error) {
    console.error('数据迁移失败:', error);
    return {
      success: false,
      error: error.message
    };
  }
};

// 迁移打卡记录，添加isValidPunch字段
async function migratePunchRecords() {
  try {
    // 获取所有没有isValidPunch字段的记录
    const records = await db.collection('punchRecords')
      .where({
        isValidPunch: _.exists(false)
      })
      .get();

    let updated = 0;
    const batchSize = 20; // 批量处理，避免超时

    for (let i = 0; i < records.data.length; i += batchSize) {
      const batch = records.data.slice(i, i + batchSize);
      
      for (const record of batch) {
        try {
          await db.collection('punchRecords')
            .doc(record._id)
            .update({
              data: {
                isValidPunch: (record.duration || 0) >= 600,
                updatedAt: db.serverDate()
              }
            });
          updated++;
        } catch (updateError) {
          console.error(`更新记录 ${record._id} 失败:`, updateError);
        }
      }
    }

    return {
      success: true,
      totalRecords: records.data.length,
      updatedRecords: updated
    };

  } catch (error) {
    console.error('迁移打卡记录失败:', error);
    throw error;
  }
}

// 创建userStats集合（如果不存在）
async function createUserStatsCollection() {
  try {
    // 尝试创建一个测试记录来确保集合存在
    const testRecord = await db.collection('userStats')
      .add({
        data: {
          _openid: 'test',
          totalDays: 0,
          consecutiveDays: 0,
          totalDuration: 0,
          lastPunchDate: '',
          cacheExpiry: new Date(),
          updatedAt: db.serverDate()
        }
      });

    // 立即删除测试记录
    await db.collection('userStats')
      .doc(testRecord._id)
      .remove();

    return {
      success: true,
      message: 'userStats集合已创建'
    };

  } catch (error) {
    if (error.errCode === -502005) {
      // 集合不存在错误，这是预期的
      return {
        success: true,
        message: 'userStats集合创建准备完成'
      };
    }
    console.error('创建userStats集合失败:', error);
    throw error;
  }
}

// 清理users集合中的统计字段（可选）
async function cleanUsersCollection() {
  try {
    // 获取所有用户记录
    const users = await db.collection('users').get();
    
    let cleaned = 0;
    
    for (const user of users.data) {
      // 检查是否有统计字段需要清理
      if (user.totalDays !== undefined || user.consecutiveDays !== undefined || user.totalDuration !== undefined) {
        try {
          // 移除统计字段，保留其他字段
          const updateData = {
            updatedAt: db.serverDate()
          };
          
          // 使用unset移除字段
          await db.collection('users')
            .doc(user._id)
            .update({
              data: updateData,
              // 注意：小程序云数据库可能不支持unset，这里先保留字段
            });
          
          cleaned++;
        } catch (updateError) {
          console.error(`清理用户 ${user._id} 失败:`, updateError);
        }
      }
    }

    return {
      success: true,
      totalUsers: users.data.length,
      cleanedUsers: cleaned,
      note: '统计字段已标记为废弃，但仍保留在数据库中'
    };

  } catch (error) {
    console.error('清理users集合失败:', error);
    throw error;
  }
}
