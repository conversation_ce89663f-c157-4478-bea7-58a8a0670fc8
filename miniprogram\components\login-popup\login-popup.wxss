/* components/login-popup/login-popup.wxss */
.popup-container {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background-color: rgba(0, 0, 0, 0.6);
  display: flex;
  justify-content: center;
  align-items: center;
  z-index: 9999;
  opacity: 0;
  visibility: hidden;
  transition: all 0.3s ease;
}

.popup-container.show {
  opacity: 1;
  visibility: visible;
}

.popup-content {
  width: 80%;
  background-color: #FFFFFF;
  border-radius: 16rpx;
  overflow: hidden;
  box-shadow: 0 4rpx 20rpx rgba(0, 0, 0, 0.1);
  display: flex;
  flex-direction: column;
}

.popup-header {
  padding: 30rpx;
  text-align: center;
  border-bottom: 1rpx solid #F3F4F6;
}

.popup-title {
  font-size: 34rpx;
  font-weight: bold;
  color: #1F2937;
}

.popup-body {
  padding: 30rpx;
}

.avatar-section {
  display: flex;
  justify-content: center;
  margin-bottom: 40rpx;
}

.avatar-wrapper {
  background: transparent;
  padding: 0;
  margin: 0;
  width: auto;
  height: auto;
  position: relative;
  display: flex;
  flex-direction: column;
  align-items: center;
  border: none;
  outline: none;
}

.avatar-wrapper::after {
  border: none;
}

.avatar {
  width: 160rpx;
  height: 160rpx;
  min-width: 160rpx;
  min-height: 160rpx;
  max-width: 160rpx;
  max-height: 160rpx;
  border-radius: 80rpx;
  border: 4rpx solid #F3F4F6;
  display: block;
  box-sizing: border-box;
}

.avatar-text {
  font-size: 24rpx;
  color: #6B7280;
  margin-top: 10rpx;
}

.nickname-section {
  margin-bottom: 20rpx;
  display: flex;
  justify-content: center;
}

.nickname-input {
  width: 100%;
  height: 80rpx;
  border: 1rpx solid #E5E7EB;
  border-radius: 8rpx;
  padding: 0 20rpx;
  box-sizing: border-box;
  font-size: 28rpx;
  color: #1F2937;
  text-align: center;
}

.popup-footer {
  padding: 30rpx;
  display: flex;
  flex-direction: column;
  gap: 20rpx;
  border-top: 1rpx solid #F3F4F6;
}

.btn {
  width: 100%;
  height: 80rpx;
  border-radius: 8rpx;
  font-size: 28rpx;
  display: flex;
  align-items: center;
  justify-content: center;
}

.btn-text {
  font-size: 28rpx;
}

.btn-primary {
  background-color: #92400E;
  color: #FFFFFF;
}

.btn-secondary {
  background-color: #F3F4F6;
  color: #92400E;
}

.btn::after {
  border: none;
}

.btn-disabled {
  opacity: 0.6;
  cursor: not-allowed;
}

.btn-primary-disabled {
  background-color: #9CA3AF;
}

.btn-secondary-disabled {
  background-color: #E5E7EB;
  color: #9CA3AF;
}

/* 隐私协议弹窗样式 */
.privacy-popup-container {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background-color: rgba(0, 0, 0, 0.7);
  display: flex;
  justify-content: center;
  align-items: center;
  z-index: 10000;
  opacity: 0;
  visibility: hidden;
  transition: all 0.3s ease;
}

.privacy-popup-container.show {
  opacity: 1;
  visibility: visible;
}

.privacy-popup-content {
  width: 85%;
  max-width: 600rpx;
  background-color: #FFFFFF;
  border-radius: 20rpx;
  overflow: hidden;
  box-shadow: 0 8rpx 32rpx rgba(0, 0, 0, 0.15);
  display: flex;
  flex-direction: column;
  transform: scale(0.9);
  transition: transform 0.3s ease;
}

.privacy-popup-container.show .privacy-popup-content {
  transform: scale(1);
}

.privacy-popup-header {
  padding: 40rpx 30rpx 20rpx;
  text-align: center;
  border-bottom: 1rpx solid #F3F4F6;
}

.privacy-popup-title {
  font-size: 36rpx;
  font-weight: bold;
  color: #1F2937;
}

.privacy-popup-body {
  padding: 30rpx;
  flex: 1;
}

.privacy-text {
  display: flex;
  flex-direction: column;
  gap: 16rpx;
  margin-bottom: 30rpx;
}

.privacy-text-item {
  font-size: 28rpx;
  color: #4B5563;
  line-height: 1.6;
  display: block;
  margin-bottom: 8rpx;
}

.privacy-actions {
  display: flex;
  justify-content: center;
  margin-bottom: 20rpx;
}

.privacy-link-btn {
  background: transparent;
  color: #92400E;
  font-size: 28rpx;
  text-decoration: underline;
  padding: 0;
  margin: 0;
  border: none;
  outline: none;
}

.privacy-link-btn::after {
  border: none;
}

.privacy-popup-footer {
  padding: 20rpx 30rpx 30rpx;
  display: flex;
  gap: 20rpx;
  border-top: 1rpx solid #F3F4F6;
}

.privacy-popup-footer .btn {
  flex: 1;
  height: 80rpx;
}