{"description": "项目配置文件", "miniprogramRoot": "miniprogram/", "cloudfunctionRoot": "cloudfunctions/", "compileType": "miniprogram", "setting": {"useCompilerPlugins": ["typescript"], "babelSetting": {"ignore": [], "disablePlugins": [], "outputPath": ""}, "coverView": false, "postcss": false, "minified": false, "enhance": true, "showShadowRootInWxmlPanel": false, "packNpmRelationList": [], "ignoreUploadUnusedFiles": true, "compileHotReLoad": false, "skylineRenderEnable": true, "es6": true, "compileWorklet": false, "uglifyFileName": false, "uploadWithSourceMap": true, "packNpmManually": false, "minifyWXSS": true, "minifyWXML": true, "localPlugins": false, "condition": false, "swc": false, "disableSWC": true, "disableUseStrict": false}, "simulatorType": "wechat", "simulatorPluginLibVersion": {}, "condition": {}, "srcMiniprogramRoot": "miniprogram/", "editorSetting": {"tabIndent": "insertSpaces", "tabSize": 2}, "libVersion": "trial", "packOptions": {"ignore": [{"type": "folder", "value": ".git"}, {"type": "file", "value": ".giti<PERSON>re"}, {"type": "folder", "value": ".cursor"}, {"type": "folder", "value": "DOCS"}, {"type": "folder", "value": "phototype"}, {"type": "file", "value": "README.md"}, {"type": "folder", "value": "node_modules"}, {"type": "suffix", "value": ".md"}, {"type": "suffix", "value": ".log"}, {"type": "suffix", "value": ".DS_Store"}, {"type": "folder", "value": ".vscode"}, {"type": "folder", "value": ".idea"}, {"type": "file", "value": "package-lock.json"}, {"type": "file", "value": "yarn.lock"}, {"type": "file", "value": "package.json"}, {"type": "file", "value": "tsconfig.json"}, {"type": "folder", "value": "typings"}, {"type": "suffix", "value": ".map"}, {"type": "suffix", "value": ".bak"}, {"type": "suffix", "value": ".tmp"}, {"type": "file", "value": "test_daka_functionality.md"}, {"type": "file", "value": "project.private.config.json"}], "include": []}, "appid": "wx2859ea2860b1219e", "cloudbaseRoot": "cloudbaserc/", "cloudfunctionTemplateRoot": "cloudfunctionTemplate/"}