<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <script src="https://cdn.tailwindcss.com"></script>
    <style>
        /* 隐藏滚动条 */
        body::-webkit-scrollbar { display: none; }
        body { -ms-overflow-style: none; scrollbar-width: none; }
    </style>
</head>
<body class="bg-[#F9F7F4] font-sans">
    <div class="flex flex-col h-screen">
        <main class="flex-1 overflow-y-auto pb-24">
            <div class="p-6">
                <h1 class="text-3xl font-bold text-slate-800">一起八段锦</h1>
                <p class="text-slate-500 mt-1">每日一练，身心康健</p>
            </div>

            <div class="px-6 space-y-6">
                <div class="bg-white rounded-xl shadow-sm overflow-hidden transform hover:scale-105 transition-transform duration-300">
                    <div class="relative aspect-video">
                        <img src="https://picsum.photos/seed/h1/800/450" alt="横屏封面" class="w-full h-full object-cover">
                        <div class="absolute bottom-2 right-2 bg-black/50 text-white text-xs px-2 py-1 rounded">12:15</div>
                    </div>
                    <div class="p-4 flex justify-between items-center">
                        <div>
                            <h3 class="font-semibold text-slate-800">国家体育总局版</h3>
                            <p class="text-sm text-slate-500">大众流行，标准规范</p>
                        </div>
                        <button class="bg-amber-800 text-white px-5 py-2 rounded-full text-sm font-semibold hover:bg-amber-900 transition-colors">开始练习</button>
                    </div>
                </div>

                <div class="bg-white rounded-xl shadow-sm overflow-hidden transform hover:scale-105 transition-transform duration-300">
                    <div class="relative aspect-[9/16]">
                        <img src="https://picsum.photos/seed/v1/450/800" alt="竖屏封面" class="w-full h-full object-cover">
                        <div class="absolute bottom-2 right-2 bg-black/50 text-white text-xs px-2 py-1 rounded">08:30</div>
                    </div>
                    <div class="p-4 flex justify-between items-center">
                        <div>
                            <h3 class="font-semibold text-slate-800">武当山传承版</h3>
                            <p class="text-sm text-slate-500">道家养生，呼吸吐纳</p>
                        </div>
                        <button class="bg-amber-800 text-white px-5 py-2 rounded-full text-sm font-semibold hover:bg-amber-900 transition-colors">开始练习</button>
                    </div>
                </div>
                
                 <div class="bg-white rounded-xl shadow-sm overflow-hidden transform hover:scale-105 transition-transform duration-300">
                    <div class="relative aspect-video">
                        <img src="https://picsum.photos/seed/h2/800/450" alt="横屏封面" class="w-full h-full object-cover">
                         <div class="absolute bottom-2 right-2 bg-black/50 text-white text-xs px-2 py-1 rounded">10:00</div>
                    </div>
                    <div class="p-4 flex justify-between items-center">
                        <div>
                            <h3 class="font-semibold text-slate-800">办公室拉伸版</h3>
                            <p class="text-sm text-slate-500">久坐放松，舒缓肩颈</p>
                        </div>
                        <button class="bg-amber-800 text-white px-5 py-2 rounded-full text-sm font-semibold hover:bg-amber-900 transition-colors">开始练习</button>
                    </div>
                </div>
            </div>
        </main>

        <nav class="fixed bottom-0 left-0 right-0 h-20 bg-white/80 backdrop-blur-sm border-t border-gray-200 flex justify-around items-center">
            <div class="text-center text-amber-800">
                <svg xmlns="http://www.w3.org/2000/svg" class="h-6 w-6 mx-auto" viewBox="0 0 20 20" fill="currentColor"><path d="M10.707 2.293a1 1 0 00-1.414 0l-7 7a1 1 0 001.414 1.414L4 10.414V17a1 1 0 001 1h2a1 1 0 001-1v-2a1 1 0 011-1h2a1 1 0 011 1v2a1 1 0 001 1h2a1 1 0 001-1v-6.586l.293.293a1 1 0 001.414-1.414l-7-7z" /></svg>
                <span class="text-xs font-semibold">首页</span>
            </div>
            <div class="text-center text-slate-500">
                <svg xmlns="http://www.w3.org/2000/svg" class="h-6 w-6 mx-auto" fill="none" viewBox="0 0 24 24" stroke="currentColor" stroke-width="2"><path stroke-linecap="round" stroke-linejoin="round" d="M8 7V3m8 4V3m-9 8h10M5 21h14a2 2 0 002-2V7a2 2 0 00-2-2H5a2 2 0 00-2 2v12a2 2 0 002 2z" /></svg>
                <span class="text-xs">打卡</span>
            </div>
            <div class="text-center text-slate-500">
                 <svg xmlns="http://www.w3.org/2000/svg" class="h-6 w-6 mx-auto" fill="none" viewBox="0 0 24 24" stroke="currentColor" stroke-width="2"><path stroke-linecap="round" stroke-linejoin="round" d="M17 20h5v-2a3 3 0 00-5.356-1.857M17 20H7m10 0v-2c0-.656-.126-1.283-.356-1.857M7 20H2v-2a3 3 0 015.356-1.857M7 20v-2c0-.656.126-1.283.356-1.857m0 0a5.002 5.002 0 019.288 0M15 7a3 3 0 11-6 0 3 3 0 016 0zm6 3a2 2 0 11-4 0 2 2 0 014 0zM7 10a2 2 0 11-4 0 2 2 0 014 0z" /></svg>
                <span class="text-xs">排名</span>
            </div>
            <div class="text-center text-slate-500">
                <svg xmlns="http://www.w3.org/2000/svg" class="h-6 w-6 mx-auto" fill="none" viewBox="0 0 24 24" stroke="currentColor" stroke-width="2"><path stroke-linecap="round" stroke-linejoin="round" d="M16 7a4 4 0 11-8 0 4 4 0 018 0zM12 14a7 7 0 00-7 7h14a7 7 0 00-7-7z" /></svg>
                <span class="text-xs">我的</span>
            </div>
        </nav>
    </div>
</body>
</html>