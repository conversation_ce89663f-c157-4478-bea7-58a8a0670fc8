<!--pages/profile/profile.wxml-->
<view class="container">
  <!-- 自定义导航栏 -->
  <view class="custom-navbar" style="padding-top: {{statusBarHeight}}px;">
    <view class="navbar-content" style="height: {{navBarHeight}}px;">
      <view class="navbar-left" bindtap="goBack">
        <view class="back-icon">‹</view>
      </view>
      <view class="navbar-title">个人资料</view>
      <view class="navbar-right"></view>
    </view>
  </view>

  <!-- 内容区域 -->
  <view class="content-area" style="padding-top: {{safeAreaBottom + 20}}px;">
    <!-- 个人资料表单 -->
    <view class="profile-form">
      <!-- 用户ID -->
      <view class="form-item">
        <view class="form-label">用户ID</view>
        <view class="form-content">
          <view class="form-text">{{userInfo.userId || '生成中...'}}</view>
        </view>
      </view>

      <!-- 头像 -->
      <view class="form-item">
        <view class="form-label">头像 <text class="required">*</text></view>
        <view class="form-content">
          <button class="avatar-wrapper" open-type="chooseAvatar" bind:chooseavatar="onChooseAvatar">
            <image class="avatar" src="{{userInfo.avatarUrl}}" mode="aspectFill"></image>
          </button>
        </view>
      </view>

      <!-- 昵称 -->
      <view class="form-item">
        <view class="form-label">昵称 <text class="required">*</text></view>
        <view class="form-content">
          <input class="form-input" type="nickname" placeholder="请输入昵称" value="{{userInfo.nickName}}" bindinput="onNicknameInput" />
        </view>
      </view>

      <!-- 签名 -->
      <view class="form-item">
        <view class="form-label">签名 <text class="required">*</text></view>
        <view class="form-content">
          <input class="form-input" placeholder="请输入个性签名" value="{{userInfo.signature}}" bindinput="onSignatureInput" />
        </view>
      </view>

      <!-- 性别 -->
      <view class="form-item">
        <view class="form-label">性别</view>
        <view class="form-content">
          <picker mode="selector" range="{{genderOptions}}" value="{{genderIndex}}" bindchange="onGenderChange">
            <view class="form-picker">{{genderOptions[genderIndex] || '请选择性别'}}</view>
          </picker>
        </view>
      </view>

      <!-- 年龄 -->
      <view class="form-item">
        <view class="form-label">年龄</view>
        <view class="form-content">
          <picker mode="selector" range="{{ageOptions}}" value="{{ageIndex}}" bindchange="onAgeChange">
            <view class="form-picker">{{ageOptions[ageIndex] || '请选择年龄'}}</view>
          </picker>
        </view>
      </view>
    </view>

    <!-- 操作按钮 -->
    <view class="action-buttons">
      <button class="btn btn-primary" bindtap="saveProfile" disabled="{{loading}}">
        <text wx:if="{{!loading}}">保存修改</text>
        <text wx:else>保存中...</text>
      </button>
      <button class="btn btn-secondary" bindtap="logout">退出登录</button>
    </view>
  </view>
</view>
