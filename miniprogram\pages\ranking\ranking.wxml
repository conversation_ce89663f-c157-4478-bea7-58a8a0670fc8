<!--排行榜页面-->
<view class="container">
  <!-- 胶囊按钮安全区域 -->
  <view class="capsule-safe-area" style="height: {{safeAreaTop}}px;"></view>

  <view class="header p-6 pb-0">
    <view class="text-3xl font-bold text-dark mb-4">排行榜</view>
    <view class="tab-bar">
      <view class="tab {{currentTab == 'total' ? 'active' : ''}}" bindtap="switchTab" data-tab="total">总天数榜</view>
      <view class="tab {{currentTab == 'consecutive' ? 'active' : ''}}" bindtap="switchTab" data-tab="consecutive">连续天数榜</view>
      <view class="tab {{currentTab == 'duration' ? 'active' : ''}}" bindtap="switchTab" data-tab="duration">总时长榜</view>
    </view>
  </view>

  <scroll-view class="content" scroll-y>
    <view wx:if="{{loading}}" class="loading-container">
      <view class="loading-text">加载中...</view>
    </view>
    <view wx:elif="{{rankingList.length === 0}}" class="empty-state">
      <view wx:if="{{!isLoggedIn}}" class="empty-content">
        <view class="empty-text">请先登录查看排行榜</view>
        <view class="empty-desc">登录后即可查看排行榜数据</view>
        <button class="retry-btn" bindtap="showLoginPopup">立即登录</button>
      </view>
      <view wx:else class="empty-content">
        <view class="empty-text">数据加载失败</view>
        <view class="empty-desc">网络异常，请重试</view>
        <button class="retry-btn" bindtap="retryLoadData">重新加载</button>
      </view>
    </view>
    <view wx:else class="px-6 py-4 space-y-2">
      <view wx:for="{{rankingList}}" wx:key="id" class="rank-item {{index < 3 ? 'top-rank' : ''}}">
        <view class="rank-number {{index === 0 ? 'rank-1' : index === 1 ? 'rank-2' : index === 2 ? 'rank-3' : ''}}">{{index + 1}}</view>
        <image class="avatar {{index < 3 ? 'large' : 'small'}}" src="{{item.avatar}}" mode="aspectFill"></image>
        <view class="user-info">
          <view class="nickname">{{item.nickname}}</view>
          <view wx:if="{{index < 3 && item.userId}}" class="user-id">ID: {{item.userId}}</view>
        </view>
        <view class="rank-value">{{item.value}} {{currentTab == 'duration' ? '分钟' : '天'}}</view>
      </view>
    </view>
  </scroll-view>

  <view class="my-rank">
    <view wx:if="{{!isLoggedIn}}" class="login-prompt">
      <button class="login-btn" bindtap="showLoginPopup">微信登录</button>
    </view>
    <view wx:else class="rank-item">
      <view class="rank-number">{{myRank.rank}}</view>
      <image class="avatar small" src="{{myRank.avatar}}"></image>
      <view class="user-info">
        <view class="nickname my-nickname">{{myRank.nickname}}</view>
      </view>
      <view class="rank-value my-value">{{myRank.value}} {{currentTab == 'duration' ? '分钟' : '天'}}</view>
    </view>
  </view>

  <!-- 登录弹窗 -->
  <login-popup show="{{showLoginPopup}}" bind:save="onLoginSuccess" bind:close="hideLoginPopup"></login-popup>
</view>