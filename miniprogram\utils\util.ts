export const formatTime = (date: Date) => {
  const year = date.getFullYear()
  const month = date.getMonth() + 1
  const day = date.getDate()
  const hour = date.getHours()
  const minute = date.getMinutes()
  const second = date.getSeconds()

  return (
    [year, month, day].map(formatNumber).join('/') +
    ' ' +
    [hour, minute, second].map(formatNumber).join(':')
  )
}

const formatNumber = (n: number) => {
  const s = n.toString()
  return s[1] ? s : '0' + s
}

export const getMenuButtonInfo = () => {
  const menuButtonInfo = wx.getMenuButtonBoundingClientRect();
  const windowInfo = wx.getWindowInfo();
  const statusBarHeight = windowInfo.statusBarHeight;

  const height = menuButtonInfo.height + (menuButtonInfo.top - statusBarHeight) * 2;
  const top = menuButtonInfo.top;
  const safeAreaTop = menuButtonInfo.top;
  const safeAreaBottom = menuButtonInfo.top + menuButtonInfo.height;

  return {
    height,
    top,
    safeAreaTop,
    safeAreaBottom,
    statusBarHeight,
    menuButtonInfo
  }
};
