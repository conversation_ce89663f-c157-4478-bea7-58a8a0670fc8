import { formatTime, getMenuButtonInfo } from '../../utils/util'

Component({
  data: {
    safeAreaTop: 0,
    logs: [],
  },
  lifetimes: {
    attached() {
      const menuInfo = getMenuButtonInfo();
      this.setData({
        safeAreaTop: menuInfo.top + menuInfo.height + 10
      });

      this.setData({
        logs: (wx.getStorageSync('logs') || []).map((log: string) => ({
          date: formatTime(new Date(log)),
          timeStamp: log
        })),
      })
    }
  },
})
