import { getMenuButtonInfo } from '../../utils/util';
import { db, userCollection, punchRecordsCollection, UserInfo, CloudFunctionResult } from '../../utils/cloud';

interface VideoInfo {
  id: number;
  title: string;
  desc: string;
  cover: string;
  duration: string;
  videoUrl?: string;
  orientation?: 'portrait' | 'landscape';
}

Page({
  data: {
    safeAreaTop: 0,
    menuButtonTop: 0,
    menuButtonHeight: 0,
    videoId: 0,
    videoInfo: null as VideoInfo | null,
    videoUrl: '',
    // 播放时间记录
    startTime: 0, // 开始播放时间戳
    totalPlayTime: 0, // 总播放时长（秒）
    lastPlayTime: 0, // 上次播放时间戳
    isPlaying: false,
    // 用户信息
    openid: '',
    userInfo: null as UserInfo | null,
    // 打卡状态
    hasCheckedIn: false, // 是否已完成打卡（10分钟）
    // 全屏状态
    isFullScreen: false,

    // 视频数据
    videos: [
      {
        id: 1,
        title: '八段锦-体大刘晓蕾版',
        desc: '竖屏播放，动作细节更清晰',
        cover: '/images/video-cover1.jpg',
        duration: '12:15',
        videoUrl: 'cloud://cloud1-5go7er9y38b6f1bd.636c-cloud1-5go7er9y38b6f1bd-1366163334/videos/baduanjin-liuxiaolei.mp4',
        orientation: 'portrait' // 竖屏视频
      },
      {
        id: 2,
        title: '广卫青年双人版',
        desc: '双人配合，横屏观看更佳',
        cover: '/images/video-cover2.jpg',
        duration: '12:17',
        videoUrl: 'cloud://cloud1-5go7er9y38b6f1bd.636c-cloud1-5go7er9y38b6f1bd-1366163334/videos/baduanjin-gwqnb.mp4',
        orientation: 'landscape' // 横屏视频
      }
    ] as VideoInfo[]
  },

  onLoad(options: any) {
    // 设置安全区域高度和胶囊按钮信息
    const menuInfo = getMenuButtonInfo();
    this.setData({
      safeAreaTop: menuInfo.safeAreaTop,
      menuButtonTop: menuInfo.menuButtonInfo.top,
      menuButtonHeight: menuInfo.menuButtonInfo.height
    });

    // 获取视频ID
    const videoId = parseInt(options.id || '1');
    const videoInfo = this.data.videos.find(v => v.id === videoId);

    if (videoInfo) {
      this.setData({
        videoId,
        videoInfo
      });

      // 如果是云存储视频，获取临时URL
      if (videoInfo.videoUrl && videoInfo.videoUrl.startsWith('cloud://')) {
        this.getCloudVideoUrl(videoInfo.videoUrl);
      } else {
        this.setData({
          videoUrl: videoInfo.videoUrl || ''
        });
      }
    }

    // 获取用户信息
    this.getUserInfo();

    // 记录开始时间
    this.setData({
      startTime: Date.now()
    });
  },

  onUnload() {
    // 页面卸载时保存播放记录
    this.updatePlayTime();
    console.log(`视频页面卸载，最终总时长: ${this.data.totalPlayTime.toFixed(2)}秒`);
    // 页面卸载时停止计时
    this.setData({
      lastPlayTime: 0
    });
    this.savePlayRecord();
  },

  onHide() {
    // 页面隐藏时更新播放时间并保存记录
    this.updatePlayTime();
    console.log(`视频页面隐藏，当前总时长: ${this.data.totalPlayTime.toFixed(2)}秒`);
    // 页面隐藏时停止计时
    this.setData({
      lastPlayTime: 0
    });
    this.savePlayRecord();
  },

  onShow() {
    console.log('视频页面显示');
    // 如果视频正在播放，重新设置播放开始时间
    if (this.data.isPlaying) {
      this.setData({
        lastPlayTime: Date.now()
      });
    }
  },

  // 获取云存储视频临时URL
  async getCloudVideoUrl(fileId: string) {
    try {
      const result = await wx.cloud.getTempFileURL({
        fileList: [fileId]
      });

      if (result.fileList && result.fileList.length > 0) {
        const tempUrl = result.fileList[0].tempFileURL;
        this.setData({
          videoUrl: tempUrl
        });
        console.log('获取云存储视频URL成功:', tempUrl);
      }
    } catch (error) {
      console.error('获取云存储视频URL失败', error);
      wx.showToast({
        title: '视频加载失败',
        icon: 'none'
      });
    }
  },

  // 获取用户信息
  async getUserInfo() {
    try {
      const userInfo = wx.getStorageSync('userInfo');
      if (userInfo && userInfo.nickName) {
        // 获取openid
        const res = await wx.cloud.callFunction({
          name: 'getOpenid'
        }) as CloudFunctionResult;

        if (res.result?.openid) {
          this.setData({
            openid: res.result.openid,
            userInfo
          });
        }
      }
    } catch (error) {
      console.error('获取用户信息失败', error);
    }
  },

  // 视频加载完成事件
  onVideoLoaded() {
    console.log('视频加载完成');
  },

  // 视频播放事件
  onVideoPlay() {
    console.log(`视频开始播放，当前总时长: ${this.data.totalPlayTime.toFixed(2)}秒`);
    this.setData({
      isPlaying: true,
      lastPlayTime: Date.now()
    });
  },

  // 视频暂停事件
  onVideoPause() {
    this.setData({
      isPlaying: false
    });
    this.updatePlayTime();
    console.log(`视频暂停，当前总时长: ${this.data.totalPlayTime.toFixed(2)}秒`);
    // 暂停时停止计时
    this.setData({
      lastPlayTime: 0
    });
    // 暂停时也保存一次记录，防止数据丢失
    this.savePlayRecord();
  },

  // 视频结束事件
  onVideoEnded() {
    this.setData({
      isPlaying: false
    });
    this.updatePlayTime();
    console.log(`视频播放结束，最终总时长: ${this.data.totalPlayTime.toFixed(2)}秒`);
    // 视频结束时停止计时
    this.setData({
      lastPlayTime: 0
    });
    // 视频结束时保存记录
    this.savePlayRecord();
  },

  // 视频时间更新事件
  onVideoTimeUpdate() {
    // 只在正常播放状态下更新时长统计（不使用视频currentTime，防止拖拽进度条影响）
    if (this.data.isPlaying) {
      this.updatePlayTime();
    }

    // 检查是否达到打卡条件
    if (this.data.isPlaying && !this.data.hasCheckedIn) {
      const playTimeMinutes = this.data.totalPlayTime / 60;
      if (playTimeMinutes >= 10) {
        // 达到10分钟，完成打卡
        this.setData({
          hasCheckedIn: true
        });
        this.showCheckInSuccess();
      }
    }
  },

  // 视频进度拖拽开始事件
  onVideoSeeking() {
    console.log('用户开始拖拽进度条');
    // 拖拽时暂停时长统计
    if (this.data.isPlaying) {
      this.updatePlayTime();
      this.setData({
        lastPlayTime: 0 // 暂停时长记录
      });
    }
  },

  // 视频进度拖拽结束事件
  onVideoSeeked() {
    console.log('用户拖拽进度条结束');
    // 拖拽结束后，如果视频在播放状态，重新开始时长统计
    if (this.data.isPlaying) {
      this.setData({
        lastPlayTime: Date.now()
      });
    }
  },

  // 全屏状态变化事件
  onVideoFullScreenChange(event: any) {
    const isFullScreen = event.detail.fullScreen;
    console.log('全屏状态变化:', isFullScreen);

    this.setData({
      isFullScreen: isFullScreen
    });

    // 全屏状态变化时，确保播放状态和时间记录正确
    if (this.data.isPlaying) {
      // 重新设置播放时间记录点，确保时长统计连续
      this.setData({
        lastPlayTime: Date.now()
      });
    }
  },

  // 更新播放时间
  updatePlayTime() {
    if (this.data.lastPlayTime > 0) {
      const currentTime = Date.now();
      const playDuration = (currentTime - this.data.lastPlayTime) / 1000; // 转换为秒

      // 移除频繁的日志输出，只在关键时机输出
      // console.log(`播放时长增加: ${playDuration.toFixed(2)}秒, 总时长: ${(this.data.totalPlayTime + playDuration).toFixed(2)}秒`);

      this.setData({
        totalPlayTime: this.data.totalPlayTime + playDuration,
        lastPlayTime: currentTime // 重新设置为当前时间，而不是0
      });
    }
  },





  // 显示打卡成功提示
  showCheckInSuccess() {
    wx.showToast({
      title: '恭喜！今日打卡成功',
      icon: 'success',
      duration: 2000
    });
  },

  // 保存播放记录
  async savePlayRecord() {
    console.log(`尝试保存播放记录: openid=${this.data.openid}, totalPlayTime=${this.data.totalPlayTime.toFixed(2)}秒`);

    if (!this.data.openid) {
      console.log('没有openid，不保存记录');
      return;
    }

    if (this.data.totalPlayTime < 5) {
      console.log(`播放时间不足5秒(${this.data.totalPlayTime.toFixed(2)}秒)，不保存记录`);
      return;
    }

    try {
      const today = new Date();
      const dateStr = `${today.getFullYear()}-${String(today.getMonth() + 1).padStart(2, '0')}-${String(today.getDate()).padStart(2, '0')}`;
      // 改为保存总秒数，而不是只保存分钟数
      const playTimeSeconds = Math.floor(this.data.totalPlayTime);

      if (playTimeSeconds < 5) {
        return; // 播放时间不足5秒，不保存
      }

      try {
        // 检查今日是否已有记录
        const existingRecord = await punchRecordsCollection
          .where({
            _openid: this.data.openid,
            date: dateStr
          })
          .get();

        if (existingRecord.data.length > 0) {
          const recordId = existingRecord.data[0]._id;
          const existingDuration = existingRecord.data[0].duration || 0;
          const newTotalDuration = existingDuration + playTimeSeconds;

          console.log(`更新现有记录: 原时长=${existingDuration}秒, 新增=${playTimeSeconds}秒, 总计=${newTotalDuration}秒`);

          if (recordId) {
            await punchRecordsCollection.doc(recordId).update({
              data: {
                duration: newTotalDuration,
                updatedAt: db.serverDate()
              }
            });
            console.log('播放记录更新成功');
          }
        } else {
          console.log(`创建新记录: 时长=${playTimeSeconds}秒`);
          // 创建新记录
          await punchRecordsCollection.add({
            data: {
              date: dateStr,
              duration: playTimeSeconds,
              createdAt: db.serverDate()
            }
          });
          console.log('播放记录创建成功');
        }
      } catch (dbError: any) {
        // 如果是集合不存在错误，直接创建新记录
        if (dbError && dbError.errCode === -502005) {
          console.warn('打卡记录集合不存在，创建新记录');
          await punchRecordsCollection.add({
            data: {
              date: dateStr,
              duration: playTimeSeconds,
              createdAt: db.serverDate()
            }
          });
        } else {
          // 其他错误，重新抛出
          throw dbError;
        }
      }

      // 更新用户统计数据（转换为分钟传递给统计函数）
      const playTimeMinutes = Math.floor(playTimeSeconds / 60);
      await this.updateUserStats(playTimeMinutes, this.data.hasCheckedIn);

    } catch (error) {
      console.error('保存播放记录失败', error);
    }
  },

  // 更新用户统计数据
  async updateUserStats(playTimeMinutes: number, hasCheckedIn: boolean) {
    try {
      if (!this.data.openid) return;

      const userRecord = await userCollection
        .where({
          _openid: this.data.openid
        })
        .get();

      if (userRecord.data.length > 0) {
        const user = userRecord.data[0];
        const userId = userRecord.data[0]._id;
        
        let updateData: any = {
          totalDuration: (user.totalDuration || 0) + playTimeMinutes,
          updatedAt: db.serverDate()
        };

        // 如果完成了打卡（10分钟以上）
        if (hasCheckedIn) {
          const today = new Date();
          const yesterday = new Date(today);
          yesterday.setDate(yesterday.getDate() - 1);
          const yesterdayStr = `${yesterday.getFullYear()}-${String(yesterday.getMonth() + 1).padStart(2, '0')}-${String(yesterday.getDate()).padStart(2, '0')}`;

          // 检查昨天是否打卡，决定连续天数
          let hadYesterdayPunch = false;
          try {
            const yesterdayRecord = await punchRecordsCollection
              .where({
                _openid: this.data.openid,
                date: yesterdayStr
              })
              .get();

            hadYesterdayPunch = yesterdayRecord.data.length > 0;
          } catch (dbError: any) {
            // 如果集合不存在，说明没有昨天的打卡记录
            if (dbError && dbError.errCode === -502005) {
              hadYesterdayPunch = false;
            } else {
              // 其他错误，默认为false
              console.error('查询昨天打卡记录失败', dbError);
              hadYesterdayPunch = false;
            }
          }
          
          updateData.totalDays = (user.totalDays || 0) + 1;
          updateData.consecutiveDays = hadYesterdayPunch ? (user.consecutiveDays || 0) + 1 : 1;
        }

        if (userId) {
          await userCollection.doc(userId).update({
            data: updateData
          });
        }
      }
    } catch (error) {
      console.error('更新用户统计数据失败', error);
    }
  },

  // 返回首页
  goBack() {
    wx.navigateBack();
  }
})
