<!--components/login-popup/login-popup.wxml-->
<view class="popup-container {{show ? 'show' : ''}}" bindtap="closePopup">
  <view class="popup-content" catchtap="preventBubbling">
    <view class="popup-header">
      <view class="popup-title">登录</view>
    </view>
    <view class="popup-body">
      <!-- 头像选择 -->
      <view class="avatar-section">
        <button class="avatar-wrapper" open-type="chooseAvatar" bind:chooseavatar="onChooseAvatar">
          <image class="avatar" src="{{avatarUrl}}" mode="aspectFill"></image>
          <view class="avatar-text">点击选择头像</view>
        </button>
      </view>

      <!-- 昵称输入 -->
      <view class="nickname-section">
        <input type="nickname" class="nickname-input" placeholder="请输入昵称" value="{{nickname}}" bindinput="onInputNickname" />
      </view>
    </view>
    <view class="popup-footer">
      <button class="btn btn-secondary {{loading ? 'btn-disabled btn-secondary-disabled' : ''}}" bindtap="useWechatInfo" disabled="{{loading}}">使用微信头像和昵称</button>
      <button class="btn btn-primary {{loading ? 'btn-disabled btn-primary-disabled' : ''}}" bindtap="saveAndLogin" disabled="{{loading}}">
        <view wx:if="{{!loading}}" class="btn-text">保存并登录</view>
        <view wx:else class="btn-text">登录中...</view>
      </button>
    </view>
  </view>

  <!-- 隐私协议弹窗 -->
  <view class="privacy-popup-container {{showPrivacyPopup ? 'show' : ''}}" bindtap="closePrivacyPopup">
    <view class="privacy-popup-content" catchtap="preventBubbling">
      <view class="privacy-popup-header">
        <view class="privacy-popup-title">隐私保护指引</view>
      </view>
      <view class="privacy-popup-body">
        <view class="privacy-text">
          <view class="privacy-text-item">感谢您使用我们的小程序！</view>
          <view class="privacy-text-item">在使用微信头像和昵称功能前，我们需要您的授权同意。</view>
          <view class="privacy-text-item">我们将严格按照{{privacyContractName}}保护您的个人信息。</view>
        </view>
        <view class="privacy-actions">
          <button class="privacy-link-btn" bindtap="openPrivacyContract">查看{{privacyContractName}}</button>
        </view>
      </view>
      <view class="privacy-popup-footer">
        <button class="btn btn-secondary" bindtap="closePrivacyPopup">拒绝</button>
        <button id="agree-privacy-btn" class="btn btn-primary" open-type="agreePrivacyAuthorization" bindagreeprivacyauthorization="agreePrivacyAuthorization">同意并继续</button>
      </view>
    </view>
  </view>
</view>