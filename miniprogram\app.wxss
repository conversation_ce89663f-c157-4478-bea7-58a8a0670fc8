/**app.wxss**/

/* 全局隐藏滚动条样式 - 保持滚动功能但隐藏滚动条 */
/* 隐藏 WebKit 浏览器的滚动条 */
::-webkit-scrollbar {
  display: none;
}

/* 隐藏所有可滚动元素的滚动条 */
scroll-view::-webkit-scrollbar,
view::-webkit-scrollbar,
page::-webkit-scrollbar {
  display: none;
}

page {
  height: 100%;
  background-color: #F9F7F4;
  font-family: -apple-system, BlinkMacSystemFont, 'Helvetica Neue', Helvetica, 'PingFang SC', 'Microsoft YaHei', Arial, sans-serif;
  box-sizing: border-box;
  color: #1F2937;
  padding-top: 0;
}

.container {
  height: 100%;
  display: flex;
  flex-direction: column;
  box-sizing: border-box;
  position: relative;
} 

/* 通用样式 */
.flex-row {
  display: flex;
  flex-direction: row;
}

.flex-col {
  display: flex;
  flex-direction: column;
}

.items-center {
  align-items: center;
}

.justify-center {
  justify-content: center;
}

.justify-between {
  justify-content: space-between;
}

.text-center {
  text-align: center;
}

/* 字体样式 */
.font-bold {
  font-weight: bold;
}

.text-sm {
  font-size: 24rpx;
}

.text-base {
  font-size: 28rpx;
}

.text-lg {
  font-size: 32rpx;
}

.text-xl {
  font-size: 36rpx;
}

.text-2xl {
  font-size: 40rpx;
}

.text-3xl {
  font-size: 48rpx;
}

.text-4xl {
  font-size: 56rpx;
}

/* 颜色样式 */
.text-primary {
  color: #92400E; /* 琥珀色/暗金色 - 主色调 */
}

.text-gray {
  color: #9CA3AF; /* 灰色 - 次要文字 */
}

.text-dark {
  color: #1F2937; /* 深灰 - 主要文字 */
}

/* 边距 */
.p-2 {
  padding: 16rpx;
}

.p-3 {
  padding: 24rpx;
}

.p-4 {
  padding: 32rpx;
}

.p-6 {
  padding: 32rpx;
}

.py-1 {
  padding-top: 8rpx;
  padding-bottom: 8rpx;
}

.px-2 {
  padding-left: 16rpx;
  padding-right: 16rpx;
}

.px-6 {
  padding-left: 32rpx;
  padding-right: 32rpx;
}

.mt-1 {
  margin-top: 8rpx;
}

.mt-2 {
  margin-top: 16rpx;
}

.mb-2 {
  margin-bottom: 16rpx;
}

.mb-4 {
  margin-bottom: 32rpx;
}

.mb-6 {
  margin-bottom: 32rpx;
}

.mr-2 {
  margin-right: 16rpx;
}

.ml-2 {
  margin-left: 16rpx;
}

.mx-3 {
  margin-left: 24rpx;
  margin-right: 24rpx;
}

/* 阴影和边框 */
.shadow-sm {
  box-shadow: 0 1px 2px 0 rgba(0, 0, 0, 0.05);
}

.rounded-full {
  border-radius: 100%;
}

.rounded-lg {
  border-radius: 16rpx;
}

.rounded-xl {
  border-radius: 24rpx;
}

/* 背景色 */
.bg-white {
  background-color: #FFFFFF;
}

.bg-primary {
  background-color: #92400E;
}

.bg-primary-light {
  background-color: #F3F4F6;
}

.bg-green-100 {
  background-color: #D1FAE5;
}

.text-green-800 {
  color: #065F46;
}

/* 添加全局样式确保页面内容不超过胶囊按钮 */
.safe-area {
  width: 100%;
  box-sizing: border-box;
  position: relative;
}

.capsule-safe-area {
  display: block;
  width: 100%;
  flex-shrink: 0;
  box-sizing: border-box;
  min-height: 1px; /* 确保即使高度为0也会占用空间 */
  position: relative;
  z-index: 100;
} 

/* 页面标题区域样式 */
.header {
  padding-top: 0;
  padding-bottom: 16rpx;
}

/* 内容区域样式 */
.content {
  flex: 1;
  overflow-y: auto;
  -webkit-overflow-scrolling: touch;
} 
