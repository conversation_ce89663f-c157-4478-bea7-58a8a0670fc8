/**index.wxss**/
.container {
  height: 100vh;
  display: flex;
  flex-direction: column;
  box-sizing: border-box;
}

.header {
  padding-bottom: 16rpx;
}

.content {
  flex: 1;
  overflow-y: auto;
  -webkit-overflow-scrolling: touch;
}

.video-list {
  padding-bottom: 160rpx;
}

.video-card {
  overflow: hidden;
  transition: transform 0.3s;
}

.video-card:active {
  transform: scale(0.98);
}

.video-cover {
  position: relative;
  overflow: hidden;
}

/* 新的16:9竖屏样式 */
.vertical-16-9 {
  height: 0;
  padding-bottom: 155%; /* 16:9的反比例，竖屏时为16/9 = 1.778，转为百分比为177.78% */
  position: relative;
}

/* 新的9:16横屏样式 */
.horizontal-9-16 {
  height: 0;
  padding-bottom: 70%; /* 9:16的反比例，横屏时为9/16 = 0.5625，转为百分比为56.25% */
  position: relative;
}

.video-cover image {
  width: 100%;
  height: 100%;
  display: block;
  position: absolute;
  top: 0;
  left: 0;
}

.duration {
  position: absolute;
  bottom: 16rpx;
  right: 16rpx;
  background-color: rgba(0, 0, 0, 0.5);
  color: white;
  font-size: 24rpx;
  padding: 8rpx 16rpx;
  border-radius: 8rpx;
}

/* 视频标题样式 */
.video-title {
  position: absolute;
  bottom: 0;
  left: 0;
  right: 0;
  padding: 30rpx 20rpx 16rpx;
  font-size: 32rpx;
  font-weight: bold;
  color: white;
  background: linear-gradient(to top, rgba(0,0,0,0.7), rgba(0,0,0,0));
}

/* 播放图标样式 */
.play-icon {
  position: absolute;
  top: 50%;
  left: 50%;
  transform: translate(-50%, -50%);
  width: 100rpx;
  height: 100rpx;
  opacity: 0.9;
  transition: all 0.3s;
  background-color: rgba(0, 0, 0, 0.5);
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
}

.icon-play {
  width: 0;
  height: 0;
  border-style: solid;
  border-width: 20rpx 0 20rpx 40rpx;
  border-color: transparent transparent transparent #ffffff;
  margin-left: 10rpx; /* 稍微偏右一点，视觉上更居中 */
}

.video-cover:active .play-icon {
  transform: translate(-50%, -50%) scale(1.1);
  opacity: 1;
}



.video-info {
  background-color: white;
}

.practice-btn {
  background-color: #92400E;
  color: white;
  font-size: 28rpx;
  font-weight: bold;
  padding: 16rpx 40rpx;
  border-radius: 100rpx;
  border: none;
  line-height: 1.5;
}

/* 按钮点击态 */
.practice-btn:active {
  background-color: #78350F;
}

.scrollarea {
  flex: 1;
  overflow-y: hidden;
}

.userinfo {
  display: flex;
  flex-direction: column;
  align-items: center;
  color: #aaa;
  width: 80%;
}

.userinfo-avatar {
  overflow: hidden;
  width: 128rpx;
  height: 128rpx;
  margin: 20rpx;
  border-radius: 50%;
}

.usermotto {
  margin-top: 200px;
}

.avatar-wrapper {
  padding: 0;
  width: 56px !important;
  border-radius: 8px;
  margin-top: 40px;
  margin-bottom: 40px;
}

.avatar {
  display: block;
  width: 56px;
  height: 56px;
}

.nickname-wrapper {
  display: flex;
  width: 100%;
  padding: 16px;
  box-sizing: border-box;
  border-top: .5px solid rgba(0, 0, 0, 0.1);
  border-bottom: .5px solid rgba(0, 0, 0, 0.1);
  color: black;
}

.nickname-label {
  width: 105px;
}

.nickname-input {
  flex: 1;
}
