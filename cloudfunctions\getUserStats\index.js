const cloud = require('wx-server-sdk');

cloud.init({
  env: cloud.DYNAMIC_CURRENT_ENV
});

const db = cloud.database();
const _ = db.command;

exports.main = async (event, context) => {
  const { OPENID } = cloud.getWXContext();
  const { openid, useCache = true } = event;
  
  const targetOpenid = openid || OPENID;

  try {
    // 如果使用缓存，先检查缓存
    if (useCache) {
      const cachedStats = await db.collection('userStats')
        .where({
          _openid: targetOpenid,
          cacheExpiry: _.gte(new Date())
        })
        .get();

      if (cachedStats.data.length > 0) {
        const stats = cachedStats.data[0];
        return {
          success: true,
          fromCache: true,
          totalDays: stats.totalDays,
          consecutiveDays: stats.consecutiveDays,
          totalDuration: stats.totalDuration,
          lastPunchDate: stats.lastPunchDate
        };
      }
    }

    // 实时计算统计数据
    const punchRecords = await db.collection('punchRecords')
      .where({
        _openid: targetOpenid,
        isValidPunch: true
      })
      .orderBy('date', 'asc')
      .get();

    let totalDays = 0;
    let consecutiveDays = 0;
    let totalDuration = 0;
    let lastPunchDate = '';

    if (punchRecords.data.length > 0) {
      // 计算总天数和总时长
      const dailyRecords = {};
      punchRecords.data.forEach(record => {
        if (!dailyRecords[record.date]) {
          dailyRecords[record.date] = 0;
        }
        dailyRecords[record.date] += record.duration;
      });

      const punchedDates = Object.keys(dailyRecords).sort();
      totalDays = punchedDates.length;
      totalDuration = Object.values(dailyRecords).reduce((sum, duration) => sum + duration, 0);
      lastPunchDate = punchedDates[punchedDates.length - 1];

      // 计算连续打卡天数
      if (punchedDates.length > 0) {
        consecutiveDays = 1;
        const today = new Date();
        const todayStr = `${today.getFullYear()}-${String(today.getMonth() + 1).padStart(2, '0')}-${String(today.getDate()).padStart(2, '0')}`;

        // 从最后一个打卡日期开始往前计算连续天数
        let currentDate = new Date(lastPunchDate);
        let currentDateStr = lastPunchDate;

        // 如果最后打卡日期不是今天，检查是否是昨天
        if (currentDateStr !== todayStr) {
          const yesterday = new Date(today);
          yesterday.setDate(yesterday.getDate() - 1);
          const yesterdayStr = `${yesterday.getFullYear()}-${String(yesterday.getMonth() + 1).padStart(2, '0')}-${String(yesterday.getDate()).padStart(2, '0')}`;
          
          if (currentDateStr !== yesterdayStr) {
            // 最后打卡不是昨天，连续天数重置为0
            consecutiveDays = 0;
          }
        }

        // 往前查找连续的打卡日期
        if (consecutiveDays > 0) {
          for (let i = punchedDates.length - 2; i >= 0; i--) {
            const prevDate = new Date(currentDate);
            prevDate.setDate(prevDate.getDate() - 1);
            const prevDateStr = `${prevDate.getFullYear()}-${String(prevDate.getMonth() + 1).padStart(2, '0')}-${String(prevDate.getDate()).padStart(2, '0')}`;

            if (punchedDates[i] === prevDateStr) {
              consecutiveDays++;
              currentDate = prevDate;
            } else {
              break;
            }
          }
        }
      }
    }

    const stats = {
      totalDays,
      consecutiveDays,
      totalDuration,
      lastPunchDate
    };

    // 缓存结果（1小时过期）
    const cacheExpiry = new Date();
    cacheExpiry.setHours(cacheExpiry.getHours() + 1);

    await db.collection('userStats')
      .where({
        _openid: targetOpenid
      })
      .remove();

    await db.collection('userStats')
      .add({
        data: {
          _openid: targetOpenid,
          ...stats,
          cacheExpiry: cacheExpiry,
          updatedAt: db.serverDate()
        }
      });

    return {
      success: true,
      fromCache: false,
      ...stats
    };

  } catch (error) {
    console.error('获取用户统计失败:', error);
    return {
      success: false,
      error: error.message
    };
  }
};
