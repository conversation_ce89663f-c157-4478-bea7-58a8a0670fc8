<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <script src="https://cdn.tailwindcss.com"></script>
    <style>
        body::-webkit-scrollbar { display: none; }
        body { -ms-overflow-style: none; scrollbar-width: none; }
    </style>
</head>
<body class="bg-[#F9F7F4] font-sans">
    <div class="flex flex-col h-screen">
        <main class="flex-1 overflow-y-auto pb-24 p-6">
            <h1 class="text-3xl font-bold text-slate-800 mb-6">我的打卡</h1>
            
            <div class="bg-white p-6 rounded-xl shadow-sm text-center mb-6">
                <p class="text-sm text-slate-500">今日练习时长</p>
                <p class="text-4xl font-bold text-slate-800 my-2">12:35</p>
                <span class="inline-block bg-green-100 text-green-800 text-xs font-semibold px-3 py-1 rounded-full">今日已打卡</span>
            </div>

            <div class="grid grid-cols-3 gap-4 text-center mb-8">
                <div>
                    <p class="text-2xl font-bold text-amber-800">128</p>
                    <p class="text-xs text-slate-500">总共打卡天数</p>
                </div>
                <div>
                    <p class="text-2xl font-bold text-amber-800">35</p>
                    <p class="text-xs text-slate-500">连续打卡天数</p>
                </div>
                <div>
                    <p class="text-2xl font-bold text-amber-800">25.6</p>
                    <p class="text-xs text-slate-500">累计练习(小时)</p>
                </div>
            </div>

            <div class="bg-white p-4 rounded-xl shadow-sm">
                <div class="flex justify-between items-center mb-4">
                    <button class="text-slate-500">&lt;</button>
                    <h3 class="font-semibold text-slate-800">2025年 6月</h3>
                    <button class="text-slate-500">&gt;</button>
                </div>
                <div class="grid grid-cols-7 text-center text-sm gap-y-1">
                    <div class="text-slate-400">日</div><div class="text-slate-400">一</div><div class="text-slate-400">二</div><div class="text-slate-400">三</div><div class="text-slate-400">四</div><div class="text-slate-400">五</div><div class="text-slate-400">六</div>
                    
                    <div class="p-2 text-slate-400">1</div>
                    <div class="p-2 text-slate-400">2</div>
                    <div class="p-2 text-slate-800 relative"><span class="bg-amber-700/80 w-8 h-8 rounded-full flex items-center justify-center text-white">3</span></div>
                    <div class="p-2 text-slate-800 relative"><span class="bg-amber-700/80 w-8 h-8 rounded-full flex items-center justify-center text-white">4</span></div>
                    <div class="p-2 text-slate-800">5</div>
                    <div class="p-2 text-slate-800">6</div>
                    <div class="p-2 text-slate-800 relative"><span class="bg-amber-700/80 w-8 h-8 rounded-full flex items-center justify-center text-white">7</span></div>
                    <div class="p-2 text-slate-800 relative"><span class="bg-amber-700/80 w-8 h-8 rounded-full flex items-center justify-center text-white">8</span></div>
                    <div class="p-2 text-slate-800 relative"><span class="bg-amber-700/80 w-8 h-8 rounded-full flex items-center justify-center text-white">9</span></div>
                    <div class="p-2 text-slate-800">10</div>
                    <div class="p-2 text-slate-800">11</div>
                    <div class="p-2 text-slate-800 relative"><span class="bg-amber-700/80 w-8 h-8 rounded-full flex items-center justify-center text-white">12</span></div>
                    <div class="p-2 text-slate-800 relative"><span class="bg-amber-700/80 w-8 h-8 rounded-full flex items-center justify-center text-white">13</span></div>
                    <div class="p-2 text-slate-800 relative"><span class="border-2 border-amber-700 w-8 h-8 rounded-full flex items-center justify-center font-bold text-amber-800">14</span></div>
                    <div class="p-2 text-slate-800">15</div>
                    <div class="p-2 text-slate-800">16</div>
                    <div class="p-2 text-slate-800">17</div>
                    <div class="p-2 text-slate-800">18</div>
                    <div class="p-2 text-slate-800">19</div>
                    <div class="p-2 text-slate-800">20</div>
                    <div class="p-2 text-slate-800">21</div>
                    </div>
            </div>

        </main>
        
        <nav class="fixed bottom-0 left-0 right-0 h-20 bg-white/80 backdrop-blur-sm border-t border-gray-200 flex justify-around items-center">
            <div class="text-center text-slate-500">
                <svg xmlns="http://www.w3.org/2000/svg" class="h-6 w-6 mx-auto" fill="none" viewBox="0 0 24 24" stroke="currentColor" stroke-width="2"><path stroke-linecap="round" stroke-linejoin="round" d="M3 12l2-2m0 0l7-7 7 7M5 10v10a1 1 0 001 1h3m10-11l2 2m-2-2v10a1 1 0 01-1 1h-3m-6 0a1 1 0 001-1v-4a1 1 0 011-1h2a1 1 0 011 1v4a1 1 0 001 1m-6 0h6" /></svg>
                <span class="text-xs">首页</span>
            </div>
            <div class="text-center text-amber-800">
                <svg xmlns="http://www.w3.org/2000/svg" class="h-6 w-6 mx-auto" viewBox="0 0 20 20" fill="currentColor"><path fill-rule="evenodd" d="M6 2a1 1 0 00-1 1v1H4a2 2 0 00-2 2v10a2 2 0 002 2h12a2 2 0 002-2V6a2 2 0 00-2-2h-1V3a1 1 0 10-2 0v1H7V3a1 1 0 00-1-1zm0 5a1 1 0 000 2h8a1 1 0 100-2H6z" clip-rule="evenodd" /></svg>
                <span class="text-xs font-semibold">打卡</span>
            </div>
            <div class="text-center text-slate-500">
                 <svg xmlns="http://www.w3.org/2000/svg" class="h-6 w-6 mx-auto" fill="none" viewBox="0 0 24 24" stroke="currentColor" stroke-width="2"><path stroke-linecap="round" stroke-linejoin="round" d="M17 20h5v-2a3 3 0 00-5.356-1.857M17 20H7m10 0v-2c0-.656-.126-1.283-.356-1.857M7 20H2v-2a3 3 0 015.356-1.857M7 20v-2c0-.656.126-1.283.356-1.857m0 0a5.002 5.002 0 019.288 0M15 7a3 3 0 11-6 0 3 3 0 016 0zm6 3a2 2 0 11-4 0 2 2 0 014 0zM7 10a2 2 0 11-4 0 2 2 0 014 0z" /></svg>
                <span class="text-xs">排名</span>
            </div>
            <div class="text-center text-slate-500">
                <svg xmlns="http://www.w3.org/2000/svg" class="h-6 w-6 mx-auto" fill="none" viewBox="0 0 24 24" stroke="currentColor" stroke-width="2"><path stroke-linecap="round" stroke-linejoin="round" d="M16 7a4 4 0 11-8 0 4 4 0 018 0zM12 14a7 7 0 00-7 7h14a7 7 0 00-7-7z" /></svg>
                <span class="text-xs">我的</span>
            </div>
        </nav>
    </div>
</body>
</html>