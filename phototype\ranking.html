<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <script src="https://cdn.tailwindcss.com"></script>
    <style>
        body::-webkit-scrollbar { display: none; }
        body { -ms-overflow-style: none; scrollbar-width: none; }
    </style>
</head>
<body class="bg-[#F9F7F4] font-sans">
    <div class="flex flex-col h-screen">
        <header class="p-6 pb-0">
            <h1 class="text-3xl font-bold text-slate-800 mb-4">排行榜</h1>
            <div class="flex border-b border-gray-200">
                <button class="flex-1 py-3 text-sm font-semibold text-amber-800 border-b-2 border-amber-800">总天数榜</button>
                <button class="flex-1 py-3 text-sm font-semibold text-slate-500">连续天数榜</button>
                <button class="flex-1 py-3 text-sm font-semibold text-slate-500">总时长榜</button>
            </div>
        </header>

        <main class="flex-1 overflow-y-auto pb-24">
            <div class="px-6 py-4 space-y-2">
                
                <div class="flex items-center bg-white p-3 rounded-lg shadow-sm">
                    <span class="text-2xl font-bold w-10 text-center text-amber-400">1</span>
                    <img src="https://picsum.photos/seed/user1/100/100" class="w-12 h-12 rounded-full mx-3">
                    <div class="flex-1">
                        <p class="font-semibold text-slate-800">清风徐来</p>
                        <p class="text-xs text-slate-400">ID: 88231</p>
                    </div>
                    <div class="text-right">
                        <p class="font-bold text-amber-800">128 天</p>
                    </div>
                </div>

                <div class="flex items-center bg-white p-3 rounded-lg shadow-sm">
                    <span class="text-xl font-bold w-10 text-center text-slate-400">2</span>
                    <img src="https://picsum.photos/seed/user2/100/100" class="w-12 h-12 rounded-full mx-3">
                    <div class="flex-1">
                        <p class="font-semibold text-slate-800">山间明月</p>
                        <p class="text-xs text-slate-400">ID: 66542</p>
                    </div>
                    <div class="text-right">
                        <p class="font-bold text-slate-700">125 天</p>
                    </div>
                </div>

                <div class="flex items-center bg-white p-3 rounded-lg shadow-sm">
                    <span class="text-lg font-bold w-10 text-center text-orange-400">3</span>
                    <img src="https://picsum.photos/seed/user3/100/100" class="w-12 h-12 rounded-full mx-3">
                    <div class="flex-1">
                        <p class="font-semibold text-slate-800">静水流深</p>
                        <p class="text-xs text-slate-400">ID: 12543</p>
                    </div>
                    <div class="text-right">
                        <p class="font-bold text-slate-700">119 天</p>
                    </div>
                </div>
                
                <div class="flex items-center bg-white/50 p-3 rounded-lg">
                    <span class="text-base font-bold w-10 text-center text-slate-400">4</span>
                    <img src="https://picsum.photos/seed/user4/100/100" class="w-10 h-10 rounded-full mx-3">
                    <p class="flex-1 font-medium text-slate-700">无问西东</p>
                    <p class="font-semibold text-slate-600">110 天</p>
                </div>
                 <div class="flex items-center bg-white/50 p-3 rounded-lg">
                    <span class="text-base font-bold w-10 text-center text-slate-400">5</span>
                    <img src="https://picsum.photos/seed/user5/100/100" class="w-10 h-10 rounded-full mx-3">
                    <p class="flex-1 font-medium text-slate-700">云卷云舒</p>
                    <p class="font-semibold text-slate-600">108 天</p>
                </div>

            </div>
        </main>

        <div class="fixed bottom-20 left-0 right-0 p-4 bg-white/80 backdrop-blur-sm">
            <div class="flex items-center p-3 rounded-lg bg-amber-100/50 border border-amber-200">
                <span class="text-base font-bold w-10 text-center text-slate-500">35</span>
                <img src="https://picsum.photos/seed/my_avatar/100/100" class="w-10 h-10 rounded-full mx-3">
                <p class="flex-1 font-semibold text-amber-900">我的昵称</p>
                <p class="font-bold text-amber-900">35 天</p>
            </div>
        </div>
        
        <nav class="fixed bottom-0 left-0 right-0 h-20 bg-white/80 backdrop-blur-sm border-t border-gray-200 flex justify-around items-center">
             <div class="text-center text-slate-500">
                <svg xmlns="http://www.w3.org/2000/svg" class="h-6 w-6 mx-auto" fill="none" viewBox="0 0 24 24" stroke="currentColor" stroke-width="2"><path stroke-linecap="round" stroke-linejoin="round" d="M3 12l2-2m0 0l7-7 7 7M5 10v10a1 1 0 001 1h3m10-11l2 2m-2-2v10a1 1 0 01-1 1h-3m-6 0a1 1 0 001-1v-4a1 1 0 011-1h2a1 1 0 011 1v4a1 1 0 001 1m-6 0h6" /></svg>
                <span class="text-xs">首页</span>
            </div>
            <div class="text-center text-slate-500">
                <svg xmlns="http://www.w3.org/2000/svg" class="h-6 w-6 mx-auto" fill="none" viewBox="0 0 24 24" stroke="currentColor" stroke-width="2"><path stroke-linecap="round" stroke-linejoin="round" d="M8 7V3m8 4V3m-9 8h10M5 21h14a2 2 0 002-2V7a2 2 0 00-2-2H5a2 2 0 00-2 2v12a2 2 0 002 2z" /></svg>
                <span class="text-xs">打卡</span>
            </div>
            <div class="text-center text-amber-800">
                 <svg xmlns="http://www.w3.org/2000/svg" class="h-6 w-6 mx-auto" viewBox="0 0 20 20" fill="currentColor"><path d="M13 6a3 3 0 11-6 0 3 3 0 016 0zM18 8a2 2 0 11-4 0 2 2 0 014 0zM14 15a4 4 0 00-8 0v3h8v-3zM6 8a2 2 0 11-4 0 2 2 0 014 0zM16 15v-3a2 2 0 00-2-2H6a2 2 0 00-2 2v3h12z" /></svg>
                <span class="text-xs font-semibold">排名</span>
            </div>
            <div class="text-center text-slate-500">
                <svg xmlns="http://www.w3.org/2000/svg" class="h-6 w-6 mx-auto" fill="none" viewBox="0 0 24 24" stroke="currentColor" stroke-width="2"><path stroke-linecap="round" stroke-linejoin="round" d="M16 7a4 4 0 11-8 0 4 4 0 018 0zM12 14a7 7 0 00-7 7h14a7 7 0 00-7-7z" /></svg>
                <span class="text-xs">我的</span>
            </div>
        </nav>
    </div>
</body>
</html>