const cloud = require('wx-server-sdk');

cloud.init({
  env: cloud.DYNAMIC_CURRENT_ENV
});

const db = cloud.database();
const _ = db.command;

exports.main = async (event, context) => {
  const { OPENID } = cloud.getWXContext();
  const { openid } = event;

  try {
    let targetOpenids = [];
    
    if (openid) {
      // 重新计算指定用户
      targetOpenids = [openid];
    } else {
      // 重新计算所有用户
      const users = await db.collection('users').get();
      targetOpenids = users.data.map(user => user._openid);
    }

    let affectedUsers = 0;

    for (const targetOpenid of targetOpenids) {
      try {
        // 清除该用户的统计缓存
        await db.collection('userStats')
          .where({
            _openid: targetOpenid
          })
          .remove();

        // 重新计算统计数据（通过调用getUserStats云函数）
        const statsResult = await cloud.callFunction({
          name: 'getUserStats',
          data: {
            openid: targetOpenid,
            useCache: false
          }
        });

        if (statsResult.result && statsResult.result.success) {
          affectedUsers++;
        }
      } catch (userError) {
        console.error(`重新计算用户 ${targetOpenid} 统计数据失败:`, userError);
      }
    }

    return {
      success: true,
      affectedUsers: affectedUsers,
      totalUsers: targetOpenids.length
    };

  } catch (error) {
    console.error('重新计算统计数据失败:', error);
    return {
      success: false,
      error: error.message
    };
  }
};
