{"name": "klaw", "version": "3.0.0", "description": "File system walker with Readable stream interface.", "main": "./src/index.js", "scripts": {"lint": "standard && standard-markdown", "test": "npm run lint && npm run unit", "unit": "tape tests/**/*.js | tap-spec"}, "repository": {"type": "git", "url": "git+https://github.com/jprichardson/node-klaw.git"}, "keywords": ["walk", "walker", "fs", "fs-extra", "readable", "streams"], "author": "<PERSON>", "license": "MIT", "bugs": {"url": "https://github.com/jprichardson/node-klaw/issues"}, "homepage": "https://github.com/jprichardson/node-klaw#readme", "dependencies": {"graceful-fs": "^4.1.9"}, "devDependencies": {"mkdirp": "^0.5.1", "rimraf": "^2.4.3", "standard": "^11.0.1", "standard-markdown": "^4.0.1", "tap-spec": "^5.0.0", "tape": "^4.2.2"}}