import { setupSafeArea, setupLoginStatusListener, cleanupLoginStatusListener, checkLoginStatus, handleLoginSuccess, handleUserLogin } from '../../utils/auth';
import { db, userCollection, UserInfo, CloudFunctionResult } from '../../utils/cloud';

interface RankingItem {
  id: number;
  nickname: string;
  userId: string;
  avatar: string;
  value: number;
  _id?: string;
  _openid?: string;
}

Page({
  data: {
    safeAreaTop: 0,
    // 当前选中的榜单类型：total(总天数), consecutive(连续天数), duration(总时长)
    currentTab: "total",
    // 排行榜数据
    rankingList: [] as RankingItem[],
    // 我的排名数据
    myRank: {
      rank: 0,
      nickname: "",
      userId: "",
      avatar: "/images/default-avatar.png",
      value: 0
    },
    // 登录状态
    isLoggedIn: false,
    openid: '',
    userInfo: null as UserInfo | null,
    // 登录弹窗显示状态
    showLoginPopup: false,
    // 加载状态
    loading: false
  },

  // 登录状态变化回调
  loginStatusChangeCallback: null as ((isLoggedIn: boolean, userInfo: UserInfo | null) => void) | null,

  onLoad() {
    setupSafeArea(this);
    setupLoginStatusListener(this, () => {
      this.loadRankingData(this.data.currentTab);
    });
    this.customCheckLoginStatus();
  },

  onShow() {
    this.customCheckLoginStatus();
  },

  onPullDownRefresh() {
    this.loadRankingData(this.data.currentTab).then(() => {
      wx.stopPullDownRefresh();
    });
  },

  onUnload() {
    cleanupLoginStatusListener(this);
  },

  async customCheckLoginStatus() {
    await checkLoginStatus(this);
    this.loadRankingData(this.data.currentTab);
  },

  switchTab(e: any) {
    const tab = e.currentTarget.dataset.tab;
    this.setData({ currentTab: tab });
    this.loadRankingData(tab);
  },
  
  async showLoginPopup() {
    await handleUserLogin(this);
  },

  hideLoginPopup() {
    this.setData({ showLoginPopup: false });
  },

  onLoginSuccess(e: any) {
    const { userInfo, openid } = e.detail;
    handleLoginSuccess(this, userInfo, openid);
    this.loadRankingData(this.data.currentTab);
  },

  // 加载排行榜数据
  async loadRankingData(type: string) {
    console.log(`开始加载排行榜数据，类型: ${type}, 登录状态: ${this.data.isLoggedIn}`);
    this.setData({ loading: true });

    try {
      console.log('调用云函数获取排行榜数据');

      // 使用新的云函数获取排行榜数据
      const result = await wx.cloud.callFunction({
        name: 'getRanking',
        data: {
          type: type,
          limit: 50
        }
      }) as CloudFunctionResult;

      if (result.result && (result.result as any).success) {
        const rankingResult = result.result as any;
        console.log('获取排行榜数据成功:', rankingResult);

        this.setData({
          rankingList: rankingResult.ranking || [],
          myRank: rankingResult.myRank || {
            rank: 0,
            nickname: "我的昵称",
            userId: "",
            avatar: "/images/default-avatar.png",
            value: 0
          }
        });
        console.log('排行榜数据设置完成');
      } else {
        throw new Error((result.result as any)?.error || '获取排行榜数据失败');
      }
    } catch (error) {
      console.error('加载排行榜数据失败', error);

      // 统一的错误处理：显示数据加载失败
      wx.showToast({
        title: '数据加载失败',
        icon: 'none',
        duration: 2000
      });

      // 清空排行榜数据，显示重试界面
      this.setData({
        rankingList: [],
        myRank: {
          rank: 0,
          nickname: "加载失败",
          userId: "",
          avatar: "/images/default-avatar.png",
          value: 0
        }
      });
    } finally {
      this.setData({ loading: false });
    }
  },



  // 重新加载数据
  retryLoadData() {
    this.loadRankingData(this.data.currentTab);
  },

  // 从云数据库获取排行榜数据
  async getRankingFromCloud(type: string): Promise<RankingItem[]> {
    try {
      let orderField = '';
      switch (type) {
        case 'total':
          orderField = 'totalDays';
          break;
        case 'consecutive':
          orderField = 'consecutiveDays';
          break;
        case 'duration':
          orderField = 'totalDuration';
          break;
        default:
          orderField = 'totalDays';
      }

      console.log(`查询排行榜数据，排序字段: ${orderField}`);

      // 先查询所有用户数量用于调试
      const totalUsersResult = await userCollection.count();
      console.log(`云数据库中总用户数: ${totalUsersResult.total}`);

      // 查询有统计数据的用户数量
      const usersWithDataResult = await userCollection
        .where({
          [orderField]: db.command.gt(0)
        })
        .count();
      console.log(`有${orderField}数据的用户数: ${usersWithDataResult.total}`);

      // 查询排行榜数据，按指定字段降序排列
      // 只查询有数据的用户，避免全表扫描
      // 建议在云数据库控制台为 totalDays、consecutiveDays、totalDuration 字段创建降序索引以提高查询性能
      const result = await userCollection
        .where({
          [orderField]: db.command.gt(0)  // 只查询该字段大于0的用户，避免空查询警告
        })
        .orderBy(orderField, 'desc')
        .limit(50)  // 限制返回前50名，减少数据传输量和查询负担
        .get();

      console.log(`云数据库查询结果，用户数量: ${result.data.length}`);

      // 打印前几个用户的数据用于调试
      if (result.data.length > 0) {
        console.log('前3个用户数据:', result.data.slice(0, 3).map(user => ({
          nickName: user.nickName,
          totalDays: user.totalDays,
          consecutiveDays: user.consecutiveDays,
          totalDuration: user.totalDuration
        })));
      }

      const rankingList: RankingItem[] = result.data.map((user: any, index: number) => {
        let value = 0;
        switch (type) {
          case 'total':
            value = user.totalDays || 0;
            break;
          case 'consecutive':
            value = user.consecutiveDays || 0;
            break;
          case 'duration':
            // 直接使用分钟数
            value = Math.round((user.totalDuration || 0) / 60);
            break;
        }

        return {
          id: index + 1,
          nickname: user.nickName || '匿名用户',
          userId: user.userId || '',
          avatar: user.avatarUrl || '/images/default-avatar.png',
          value: value,
          _id: user._id,
          _openid: user._openid
        };
      });

      console.log(`处理后的排行榜数据，数量: ${rankingList.length}`);
      return rankingList;
    } catch (error) {
      console.error('从云数据库获取排行榜数据失败', error);
      throw error;
    }
  },

  // 获取用户排名
  async getUserRanking(type: string): Promise<any> {
    try {
      if (!this.data.userInfo || !this.data.openid) {
        return {
          rank: 0,
          nickname: "我的昵称",
          userId: "",
          avatar: "/images/default-avatar.png",
          value: 0
        };
      }

      let orderField = '';
      switch (type) {
        case 'total':
          orderField = 'totalDays';
          break;
        case 'consecutive':
          orderField = 'consecutiveDays';
          break;
        case 'duration':
          orderField = 'totalDuration';
          break;
        default:
          orderField = 'totalDays';
      }

      // 获取当前用户的数据
      const currentUserValue = this.data.userInfo[orderField as keyof UserInfo] || 0;

      // 查询比当前用户分数高的用户数量来确定排名
      const higherScoreResult = await userCollection
        .where({
          [orderField]: db.command.gt(currentUserValue)
        })
        .count();

      const rank = higherScoreResult.total + 1;

      // 计算显示值
      let displayValue = 0;
      switch (type) {
        case 'total':
          displayValue = this.data.userInfo.totalDays || 0;
          break;
        case 'consecutive':
          displayValue = this.data.userInfo.consecutiveDays || 0;
          break;
        case 'duration':
          // 直接使用分钟数
          displayValue = Math.round((this.data.userInfo.totalDuration || 0) / 60);
          break;
      }

      return {
        rank: rank,
        nickname: this.data.userInfo.nickName || '我的昵称',
        userId: this.data.userInfo.userId || '',
        avatar: this.data.userInfo.avatarUrl || '/images/default-avatar.png',
        value: displayValue
      };
    } catch (error) {
      console.error('获取用户排名失败', error);
      return {
        rank: 0,
        nickname: this.data.userInfo?.nickName || "我的昵称",
        userId: this.data.userInfo?.userId || "",
        avatar: this.data.userInfo?.avatarUrl || "/images/default-avatar.png",
        value: 0
      };
    }
  }
})