<execution>
  <constraint>
    ## 微信小程序开发客观限制
    - **包体积限制**：主包不超过2MB，总包不超过20MB
    - **API调用限制**：网络请求并发限制，部分敏感API需要用户授权
    - **审核规范约束**：必须符合微信小程序审核规范和内容政策
    - **云开发配额**：云函数调用次数、数据库读写次数、存储空间有配额限制
    - **平台兼容性**：需要考虑不同微信版本和手机系统的兼容性
  </constraint>

  <rule>
    ## 强制性开发规则
    - **代码规范强制**：严格遵循微信小程序代码规范和ESLint配置
    - **安全规则强制**：用户数据加密存储，敏感操作必须验证用户身份
    - **性能规则强制**：页面加载时间不超过3秒，首屏渲染时间不超过1秒
    - **云开发规范**：云函数必须包含错误处理，数据库操作必须有权限控制
    - **版本控制强制**：每个功能开发完成必须提交Git，重要版本必须打tag
  </rule>

  <guideline>
    ## 开发指导原则
    - **用户体验优先**：始终从用户角度思考功能设计和交互流程
    - **渐进式开发**：先实现核心功能，再逐步完善辅助功能
    - **组件化思维**：优先考虑组件复用，减少重复代码
    - **性能意识**：开发过程中持续关注性能指标，及时优化
    - **文档同步**：重要功能和接口必须同步更新开发文档
  </guideline>

  <process>
    ## 微信小程序全栈开发标准流程
    
    ### Phase 1: 项目初始化 (1-2天)
    
    ```mermaid
    flowchart TD
        A[创建小程序项目] --> B[配置云开发环境]
        B --> C[设置项目结构]
        C --> D[配置开发工具]
        D --> E[初始化Git仓库]
        E --> F[创建开发分支]
    ```
    
    **具体执行步骤**：
    1. 在微信开发者工具中创建新项目
    2. 开通云开发服务，配置环境ID
    3. 设置标准目录结构：pages/components/utils/cloud-functions
    4. 配置ESLint、prettier等代码规范工具
    5. 初始化Git并创建develop分支
    
    ### Phase 2: 架构设计 (2-3天)
    
    ```mermaid
    graph TD
        A[需求分析] --> B[数据库设计]
        B --> C[API接口设计]
        C --> D[页面结构设计]
        D --> E[组件规划]
        E --> F[状态管理设计]
    ```
    
    **设计要点**：
    - 根据业务需求设计云数据库集合结构
    - 规划云函数接口和数据交互格式
    - 设计页面导航结构和用户流程
    - 规划可复用组件和工具函数
    - 确定全局状态管理策略
    
    ### Phase 3: 后端开发 (3-5天)
    
    ```mermaid
    flowchart TD
        A[云数据库设计] --> B[云函数开发]
        B --> C[数据库权限配置]
        C --> D[API接口测试]
        D --> E[云存储配置]
        E --> F[后端功能验证]
    ```
    
    **开发重点**：
    - 创建数据库集合并设置索引
    - 开发核心业务云函数
    - 配置数据库读写权限
    - 使用云开发控制台测试接口
    - 配置云存储和CDN加速
    
    ### Phase 4: 前端开发 (5-8天)
    
    ```mermaid
    flowchart TD
        A[页面框架搭建] --> B[组件开发]
        B --> C[业务逻辑实现]
        C --> D[样式美化]
        D --> E[交互优化]
        E --> F[前端测试]
    ```
    
    **开发流程**：
    - 搭建页面基础框架和导航
    - 开发可复用的业务组件
    - 实现核心业务逻辑和数据绑定
    - 完善UI样式和响应式布局
    - 优化用户交互体验
    - 进行功能测试和兼容性测试
    
    ### Phase 5: 集成测试 (2-3天)
    
    ```mermaid
    flowchart TD
        A[前后端联调] --> B[功能测试]
        B --> C[性能测试]
        C --> D[兼容性测试]
        D --> E[用户体验测试]
        E --> F[问题修复]
    ```
    
    **测试要点**：
    - 验证前后端数据交互正确性
    - 测试所有功能模块的完整性
    - 检查页面加载速度和响应时间
    - 测试不同设备和微信版本兼容性
    - 邀请用户进行体验测试
    
    ### Phase 6: 优化发布 (1-2天)
    
    ```mermaid
    flowchart TD
        A[代码优化] --> B[性能优化]
        B --> C[包体积优化]
        C --> D[提交审核]
        D --> E[发布上线]
        E --> F[监控反馈]
    ```
    
    **发布准备**：
    - 代码review和重构优化
    - 图片压缩和资源优化
    - 分包加载和懒加载优化
    - 提交微信审核
    - 发布正式版本
    - 设置用户反馈收集机制
  </process>

  <criteria>
    ## 开发质量评价标准
    
    ### 功能完整性
    - ✅ 所有需求功能正常运行
    - ✅ 异常情况有合理的错误处理
    - ✅ 用户操作流程顺畅无阻塞
    - ✅ 数据存储和读取准确无误
    
    ### 性能指标
    - ✅ 首屏加载时间 < 1秒
    - ✅ 页面切换响应时间 < 500ms
    - ✅ 网络请求响应时间 < 2秒
    - ✅ 内存占用控制在合理范围
    
    ### 代码质量
    - ✅ 代码规范符合ESLint配置
    - ✅ 组件复用率 > 60%
    - ✅ 代码注释覆盖率 > 30%
    - ✅ 单元测试覆盖核心功能
    
    ### 用户体验
    - ✅ 界面美观符合微信设计规范
    - ✅ 交互流程符合用户习惯
    - ✅ 错误提示友好易懂
    - ✅ 加载状态有明确反馈
    
    ### 安全合规
    - ✅ 用户数据加密存储
    - ✅ 敏感操作有权限验证
    - ✅ 符合微信审核规范
    - ✅ 隐私政策完整合规
  </criteria>
</execution>
