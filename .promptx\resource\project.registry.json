{"version": "2.0.0", "source": "project", "metadata": {"version": "2.0.0", "description": "project 级资源注册表", "createdAt": "2025-07-20T16:22:50.931Z", "updatedAt": "2025-07-20T16:22:50.937Z", "resourceCount": 3}, "resources": [{"id": "miniprogram-development-workflow", "source": "project", "protocol": "execution", "name": "Miniprogram Development Workflow 执行模式", "description": "执行模式，定义具体的行为模式", "reference": "@project://.promptx/resource/role/wechat-miniprogram-fullstack/execution/miniprogram-development-workflow.execution.md", "metadata": {"createdAt": "2025-07-20T16:22:50.934Z", "updatedAt": "2025-07-20T16:22:50.934Z", "scannedAt": "2025-07-20T16:22:50.934Z", "path": "role/wechat-miniprogram-fullstack/execution/miniprogram-development-workflow.execution.md"}}, {"id": "miniprogram-thinking", "source": "project", "protocol": "thought", "name": "Miniprogram Thinking 思维模式", "description": "思维模式，指导AI的思考方式", "reference": "@project://.promptx/resource/role/wechat-miniprogram-fullstack/thought/miniprogram-thinking.thought.md", "metadata": {"createdAt": "2025-07-20T16:22:50.935Z", "updatedAt": "2025-07-20T16:22:50.935Z", "scannedAt": "2025-07-20T16:22:50.935Z", "path": "role/wechat-miniprogram-fullstack/thought/miniprogram-thinking.thought.md"}}, {"id": "wechat-miniprogram-fullstack", "source": "project", "protocol": "role", "name": "Wechat Miniprogram Fullstack 角色", "description": "专业角色，提供特定领域的专业能力", "reference": "@project://.promptx/resource/role/wechat-miniprogram-fullstack/wechat-miniprogram-fullstack.role.md", "metadata": {"createdAt": "2025-07-20T16:22:50.936Z", "updatedAt": "2025-07-20T16:22:50.936Z", "scannedAt": "2025-07-20T16:22:50.936Z", "path": "role/wechat-miniprogram-fullstack/wechat-miniprogram-fullstack.role.md"}}], "stats": {"totalResources": 3, "byProtocol": {"execution": 1, "thought": 1, "role": 1}, "bySource": {"project": 3}}}