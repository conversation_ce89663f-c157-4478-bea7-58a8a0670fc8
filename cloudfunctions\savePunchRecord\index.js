const cloud = require('wx-server-sdk');

cloud.init({
  env: cloud.DYNAMIC_CURRENT_ENV
});

const db = cloud.database();
const _ = db.command;

exports.main = async (event, context) => {
  const { OPENID } = cloud.getWXContext();
  const { date, duration } = event;

  try {
    // 参数验证
    if (!date || !duration || duration < 0) {
      return {
        success: false,
        error: '参数错误'
      };
    }

    const isValidPunch = duration >= 600; // 10分钟 = 600秒

    // 查询是否已有今日记录
    const existingRecord = await db.collection('punchRecords')
      .where({
        _openid: OPENID,
        date: date
      })
      .get();

    let result;
    let totalDuration;

    if (existingRecord.data.length > 0) {
      // 更新现有记录
      const recordId = existingRecord.data[0]._id;
      const existingDuration = existingRecord.data[0].duration || 0;
      totalDuration = existingDuration + duration;
      
      result = await db.collection('punchRecords')
        .doc(recordId)
        .update({
          data: {
            duration: totalDuration,
            isValidPunch: totalDuration >= 600,
            updatedAt: db.serverDate()
          }
        });
    } else {
      // 创建新记录
      totalDuration = duration;
      result = await db.collection('punchRecords')
        .add({
          data: {
            _openid: OPENID,
            date: date,
            duration: totalDuration,
            isValidPunch: isValidPunch,
            createdAt: db.serverDate(),
            updatedAt: db.serverDate()
          }
        });
    }

    // 清除用户统计缓存
    await db.collection('userStats')
      .where({
        _openid: OPENID
      })
      .remove();

    return {
      success: true,
      isValidPunch: totalDuration >= 600,
      totalDuration: totalDuration,
      recordId: result._id || existingRecord.data[0]._id
    };

  } catch (error) {
    console.error('保存打卡记录失败:', error);
    return {
      success: false,
      error: error.message
    };
  }
};
