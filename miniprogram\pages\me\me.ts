import { getMenuButtonInfo } from '../../utils/util';
import { db, userCollection, UserInfo, CloudFunctionResult } from '../../utils/cloud';

Page({
  data: {
    safeAreaTop: 0,
    userInfo: {
      avatarUrl: '',
      nickName: '',
      signature: '坚持，是一种力量'
    } as UserInfo,
    hasUserInfo: false,
    openid: '',
    showLoginPopup: false
  },

  // 登录状态变化回调
  loginStatusChangeCallback: null as ((isLoggedIn: boolean, userInfo: UserInfo | null) => void) | null,

  onLoad() {
    // 获取胶囊按钮信息
    const menuInfo = getMenuButtonInfo();
    // 设置安全区域高度为胶囊按钮上边线
    this.setData({
      safeAreaTop: menuInfo.safeAreaTop
    });

    // 注册全局登录状态变化监听
    const app = getApp<IAppOption>();
    this.loginStatusChangeCallback = (isLoggedIn: boolean, userInfo: UserInfo | null) => {
      if (isLoggedIn && userInfo) {
        this.setData({
          userInfo: userInfo,
          hasUserInfo: true,
          openid: app.globalData.openid
        });
      } else {
        this.setData({
          userInfo: {
            avatarUrl: '',
            nickName: '',
            signature: '坚持，是一种力量'
          },
          hasUserInfo: false,
          openid: ''
        });
      }
    };

    if (app.onLoginStatusChange) {
      app.onLoginStatusChange(this.loginStatusChangeCallback);
    }

    // 获取用户登录状态
    this.checkUserLoginStatus();
  },

  onShow() {
    // 页面显示时重新检查登录状态
    this.checkUserLoginStatus();
  },

  onUnload() {
    // 页面卸载时移除登录状态监听
    const app = getApp<IAppOption>();
    if (app.offLoginStatusChange && this.loginStatusChangeCallback) {
      app.offLoginStatusChange(this.loginStatusChangeCallback);
    }
  },

  // 检查用户登录状态
  async checkUserLoginStatus() {
    try {
      // 检查是否主动退出登录
      const userLoggedOut = wx.getStorageSync('userLoggedOut');
      if (userLoggedOut) {
        // 用户主动退出登录，不自动恢复登录状态
        this.clearLoginState();
        return;
      }

      // 首先检查全局状态
      const app = getApp<IAppOption>();
      if (app.globalData.isLoggedIn && app.globalData.userInfo) {
        this.setData({
          userInfo: app.globalData.userInfo,
          hasUserInfo: true,
          openid: app.globalData.openid
        });
        return;
      }

      // 获取用户openid
      const openidResult = await this.getOpenid();
      if (!openidResult) {
        // 如果获取openid失败，清除本地状态
        this.clearLoginState();
        return;
      }

      // 优先从云数据库获取最新用户信息
      const userResult = await userCollection.where({
        _openid: this.data.openid
      }).get();

      if (userResult.data.length > 0) {
        // 已注册用户，获取云端用户信息
        const userData = userResult.data[0];
        const userInfo = {
          avatarUrl: userData.avatarUrl,
          nickName: userData.nickName,
          signature: userData.signature || '坚持，是一种力量',
          userId: userData.userId || '',
          totalDays: userData.totalDays || 0,
          consecutiveDays: userData.consecutiveDays || 0,
          totalDuration: userData.totalDuration || 0
        };

        this.setData({
          userInfo: userInfo,
          hasUserInfo: true
        });

        // 同步更新本地存储
        wx.setStorageSync('userInfo', userInfo);

        // 更新全局状态
        if (app.setGlobalLoginStatus) {
          app.setGlobalLoginStatus(true, userInfo, this.data.openid);
        }
      } else {
        // 云端没有用户数据，检查本地存储
        const localUserInfo = wx.getStorageSync('userInfo');
        if (localUserInfo && localUserInfo.nickName) {
          // 本地有数据但云端没有，可能是数据同步问题，尝试重新保存到云端
          try {
            await this.saveUserToCloud(localUserInfo);
            this.setData({
              userInfo: localUserInfo,
              hasUserInfo: true
            });

            // 更新全局状态
            if (app.setGlobalLoginStatus) {
              app.setGlobalLoginStatus(true, localUserInfo, this.data.openid);
            }
          } catch (error) {
            console.error('同步本地数据到云端失败', error);
            // 同步失败，清除本地状态
            this.clearLoginState();
          }
        } else {
          // 本地也没有数据，清除登录状态
          this.clearLoginState();
        }
      }
    } catch (error) {
      console.error('检查用户登录状态失败', error);
      // 发生错误时，尝试使用本地存储的数据
      const localUserInfo = wx.getStorageSync('userInfo');
      if (localUserInfo && localUserInfo.nickName) {
        this.setData({
          userInfo: localUserInfo,
          hasUserInfo: true
        });
      } else {
        this.clearLoginState();
      }
    }
  },

  // 清除登录状态
  clearLoginState() {
    this.setData({
      userInfo: {
        avatarUrl: '',
        nickName: '',
        signature: '坚持，是一种力量'
      },
      hasUserInfo: false,
      openid: ''
    });

    // 清除本地存储
    wx.removeStorageSync('userInfo');

    // 更新全局状态
    const app = getApp<IAppOption>();
    if (app.setGlobalLoginStatus) {
      app.setGlobalLoginStatus(false, null, '');
    }
  },

  // 获取用户openid
  async getOpenid() {
    try {
      const res = await wx.cloud.callFunction({
        name: 'getOpenid'
      }) as CloudFunctionResult;
      
      if (res.result && res.result.openid) {
        this.setData({
          openid: res.result.openid
        });
        return true;
      }
      return false;
    } catch (error) {
      console.error('获取openid失败', error);
      return false;
    }
  },
  
  // 微信登录
  async onGetUserInfo(e: any) {
    if (!e.detail.userInfo) {
      wx.showToast({
        title: '授权失败',
        icon: 'none'
      });
      return;
    }

    try {
      // 获取用户信息
      const userInfo: UserInfo = {
        avatarUrl: e.detail.userInfo.avatarUrl,
        nickName: e.detail.userInfo.nickName,
        signature: '坚持，是一种力量'
      };

      // 保存到本地存储
      wx.setStorageSync('userInfo', userInfo);

      // 设置页面数据
      this.setData({
        userInfo: userInfo,
        hasUserInfo: true
      });

      // 确保有openid
      if (!this.data.openid) {
        await this.getOpenid();
      }

      // 保存到云数据库
      await this.saveUserToCloud(userInfo);

      wx.showToast({
        title: '登录成功',
        icon: 'success'
      });
    } catch (error) {
      console.error('微信登录失败', error);
      wx.showToast({
        title: '登录失败，请重试',
        icon: 'none'
      });
    }
  },

  // 保存用户信息到云数据库
  async saveUserToCloud(userInfo: UserInfo) {
    try {
      // 查询用户是否已存在
      const queryResult = await userCollection.where({
        _openid: this.data.openid
      }).get();

      if (queryResult.data.length === 0) {
        // 新用户，添加记录
        await userCollection.add({
          data: {
            ...userInfo,
            totalDays: 0,
            consecutiveDays: 0,
            totalDuration: 0,
            createdAt: db.serverDate(),
            updatedAt: db.serverDate()
          }
        });
      } else {
        // 已有用户，更新信息
        const docId = queryResult.data[0]._id;
        if (docId) {
          await userCollection.doc(docId).update({
            data: {
              ...userInfo,
              updatedAt: db.serverDate()
            }
          });
        }
      }
    } catch (error) {
      console.error('保存用户信息失败', error);
      throw error;
    }
  },
  
  // 编辑个人资料
  editProfile() {
    wx.navigateTo({
      url: '/pages/profile/profile'
    });
  },
  
  // 选择头像
  async chooseAvatar() {
    try {
      const res = await new Promise<WechatMiniprogram.ChooseImageSuccessCallbackResult>((resolve, reject) => {
    wx.chooseImage({
      count: 1,
      sizeType: ['compressed'],
      sourceType: ['album', 'camera'],
          success: resolve,
          fail: reject
        });
      });
      
      const tempFilePath = res.tempFilePaths[0];
      
      // 上传头像到云存储
      wx.showLoading({ title: '上传中...' });
      const uploadResult = await wx.cloud.uploadFile({
        cloudPath: `avatars/${this.data.openid}_${Date.now()}.jpg`,
        filePath: tempFilePath
      });
      
      if (!uploadResult.fileID) {
        throw new Error('上传失败');
      }
      
      // 更新用户头像
      const userInfo = this.data.userInfo;
      userInfo.avatarUrl = uploadResult.fileID;
      
      // 更新本地数据
      this.setData({ userInfo });
      wx.setStorageSync('userInfo', userInfo);
      
      // 更新云数据库
      await this.updateUserField('avatarUrl', uploadResult.fileID);
      
      wx.hideLoading();
      wx.showToast({ title: '头像已更新', icon: 'success' });
    } catch (error) {
      wx.hideLoading();
      console.error('修改头像失败', error);
      wx.showToast({ title: '修改头像失败', icon: 'none' });
    }
  },
  
  // 修改昵称
  async editNickname() {
    try {
      const res = await new Promise<WechatMiniprogram.ShowModalSuccessCallbackResult>((resolve, reject) => {
    wx.showModal({
      title: '修改昵称',
      editable: true,
      placeholderText: '请输入昵称',
      content: this.data.userInfo.nickName || '',
          success: resolve,
          fail: reject
        });
      });
      
        if (res.confirm && res.content) {
        const nickname = res.content.trim();
        if (nickname === '') {
          wx.showToast({
            title: '昵称不能为空',
            icon: 'none'
          });
          return;
        }
        
        // 更新用户昵称
        const userInfo = this.data.userInfo;
        userInfo.nickName = nickname;
        
        // 更新本地数据
        this.setData({ userInfo });
        wx.setStorageSync('userInfo', userInfo);
        
        // 更新云数据库
        await this.updateUserField('nickName', nickname);
        
        wx.showToast({ title: '昵称已更新', icon: 'success' });
      }
    } catch (error) {
      console.error('修改昵称失败', error);
      wx.showToast({ title: '修改昵称失败', icon: 'none' });
    }
  },
  
  // 修改签名
  async editSignature() {
    try {
      const res = await new Promise<WechatMiniprogram.ShowModalSuccessCallbackResult>((resolve, reject) => {
    wx.showModal({
      title: '修改签名',
      editable: true,
      placeholderText: '请输入个性签名',
      content: this.data.userInfo.signature || '',
          success: resolve,
          fail: reject
        });
      });
      
        if (res.confirm) {
        const signature = res.content || '坚持，是一种力量';
        
        // 更新用户签名
          const userInfo = this.data.userInfo;
        userInfo.signature = signature;
        
        // 更新本地数据
        this.setData({ userInfo });
        wx.setStorageSync('userInfo', userInfo);
        
        // 更新云数据库
        await this.updateUserField('signature', signature);
        
        wx.showToast({ title: '签名已更新', icon: 'success' });
      }
    } catch (error) {
      console.error('修改签名失败', error);
      wx.showToast({ title: '修改签名失败', icon: 'none' });
    }
  },
  
  // 更新用户字段
  async updateUserField(field: string, value: any) {
    try {
      // 查询用户记录
      const queryResult = await userCollection.where({
        _openid: this.data.openid
      }).get();
      
      if (queryResult.data.length > 0) {
        const updateData: Record<string, any> = {
          updatedAt: db.serverDate()
        };
        updateData[field] = value;
        
        // 确保有文档ID
        const docId = queryResult.data[0]._id;
        if (docId) {
          // 更新记录
          await userCollection.doc(docId).update({
            data: updateData
          });
        }
      }
    } catch (error) {
      console.error(`更新用户${field}失败`, error);
      throw error;
    }
  },

  // 显示登录弹窗
  showLoginPopup() {
    this.setData({
      showLoginPopup: true
    });
  },
  
  // 隐藏登录弹窗
  hideLoginPopup() {
    this.setData({
      showLoginPopup: false
    });
  },

  // 登录成功处理
  onLoginSuccess(e: any) {
    const { userInfo, openid } = e.detail;

    // 清除退出登录标记
    wx.removeStorageSync('userLoggedOut');

    // 更新页面数据
    this.setData({
      userInfo: userInfo,
      hasUserInfo: true,
      openid: openid,
      showLoginPopup: false
    });

    // 更新全局登录状态
    const app = getApp<IAppOption>();
    if (app.setGlobalLoginStatus) {
      app.setGlobalLoginStatus(true, userInfo, openid);
    }
  },
  
  // 分享小程序
  onShareAppMessage() {
    return {
      title: '一起八段锦 - 每日一练，身心康健',
      path: '/pages/index/index',
      imageUrl: '/images/share-cover.jpg'
    }
  }
}) 