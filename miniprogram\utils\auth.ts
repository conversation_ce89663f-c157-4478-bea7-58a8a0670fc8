import { getMenuButtonInfo } from './util';
import { getOpenid, getUserInfoFromCloud, UserInfo } from './cloud';

export interface LoginStatusCallback {
  (isLoggedIn: boolean, userInfo: UserInfo | null): void;
}

export const setupSafeArea = (page: any) => {
  const menuInfo = getMenuButtonInfo();
  page.setData({
    safeAreaTop: menuInfo.safeAreaTop
  });
};

export const setupLoginStatusListener = (page: any, callback?: LoginStatusCallback) => {
  const app = getApp<IAppOption>();
  
  const loginStatusChangeCallback = (isLoggedIn: boolean, userInfo: UserInfo | null) => {
    page.setData({
      isLoggedIn,
      userInfo,
      openid: app.globalData.openid
    });
    callback?.(isLoggedIn, userInfo);
  };

  page.loginStatusChangeCallback = loginStatusChangeCallback;

  if (app.onLoginStatusChange) {
    app.onLoginStatusChange(loginStatusChangeCallback);
  }
};

export const cleanupLoginStatusListener = (page: any) => {
  const app = getApp<IAppOption>();
  if (app.offLoginStatusChange && page.loginStatusChangeCallback) {
    app.offLoginStatusChange(page.loginStatusChangeCallback);
  }
};

export const checkLoginStatus = async (page: any): Promise<void> => {
  try {
    const userLoggedOut = wx.getStorageSync('userLoggedOut');
    if (userLoggedOut) {
      page.setData({
        isLoggedIn: false,
        userInfo: null,
        openid: ''
      });
      return;
    }

    const app = getApp<IAppOption>();
    if (app.globalData.isLoggedIn && app.globalData.userInfo) {
      page.setData({
        isLoggedIn: app.globalData.isLoggedIn,
        userInfo: app.globalData.userInfo,
        openid: app.globalData.openid
      });
      return;
    }

    const userInfo = wx.getStorageSync('userInfo');
    if (userInfo?.nickName) {
      const openid = await getOpenid();
      if (openid) {
        const cloudUserInfo = await getUserInfoFromCloud(openid);
        if (cloudUserInfo) {
          page.setData({
            isLoggedIn: true,
            userInfo: cloudUserInfo,
            openid
          });

          if (app.setGlobalLoginStatus) {
            app.setGlobalLoginStatus(true, cloudUserInfo, openid);
          }
        }
      }
    }
  } catch (error) {
    console.error('检查登录状态失败', error);
  }
};

export const handleLoginSuccess = (page: any, userInfo: UserInfo, openid: string) => {
  wx.removeStorageSync('userLoggedOut');
  
  page.setData({
    isLoggedIn: true,
    userInfo,
    openid,
    showLoginPopup: false
  });

  const app = getApp<IAppOption>();
  if (app.setGlobalLoginStatus) {
    app.setGlobalLoginStatus(true, userInfo, openid);
  }
};
