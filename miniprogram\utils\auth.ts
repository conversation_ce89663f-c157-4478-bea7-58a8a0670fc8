import { getMenuButtonInfo } from './util';
import { getOpenid, getUserInfoFromCloud, UserInfo } from './cloud';

export interface LoginStatusCallback {
  (isLoggedIn: boolean, userInfo: UserInfo | null): void;
}

export const setupSafeArea = (page: any) => {
  const menuInfo = getMenuButtonInfo();
  page.setData({
    safeAreaTop: menuInfo.safeAreaTop
  });
};

export const setupLoginStatusListener = (page: any, callback?: LoginStatusCallback) => {
  const app = getApp<IAppOption>();
  
  const loginStatusChangeCallback = (isLoggedIn: boolean, userInfo: UserInfo | null) => {
    page.setData({
      isLoggedIn,
      userInfo,
      openid: app.globalData.openid
    });
    callback?.(isLoggedIn, userInfo);
  };

  page.loginStatusChangeCallback = loginStatusChangeCallback;

  if (app.onLoginStatusChange) {
    app.onLoginStatusChange(loginStatusChangeCallback);
  }
};

export const cleanupLoginStatusListener = (page: any) => {
  const app = getApp<IAppOption>();
  if (app.offLoginStatusChange && page.loginStatusChangeCallback) {
    app.offLoginStatusChange(page.loginStatusChangeCallback);
  }
};

export const checkLoginStatus = async (page: any): Promise<void> => {
  try {
    const userLoggedOut = wx.getStorageSync('userLoggedOut');
    if (userLoggedOut) {
      page.setData({
        isLoggedIn: false,
        userInfo: null,
        openid: ''
      });
      return;
    }

    const app = getApp<IAppOption>();
    if (app.globalData.isLoggedIn && app.globalData.userInfo) {
      page.setData({
        isLoggedIn: app.globalData.isLoggedIn,
        userInfo: app.globalData.userInfo,
        openid: app.globalData.openid
      });
      return;
    }

    // 只检查本地存储，不自动从云端恢复登录状态
    const userInfo = wx.getStorageSync('userInfo');
    if (userInfo?.nickName) {
      const openid = await getOpenid();
      if (openid) {
        page.setData({
          isLoggedIn: true,
          userInfo: userInfo,
          openid
        });

        if (app.setGlobalLoginStatus) {
          app.setGlobalLoginStatus(true, userInfo, openid);
        }
      } else {
        // 获取openid失败，清除登录状态
        page.setData({
          isLoggedIn: false,
          userInfo: null,
          openid: ''
        });
      }
    } else {
      // 本地没有数据，显示未登录状态
      page.setData({
        isLoggedIn: false,
        userInfo: null,
        openid: ''
      });
    }
  } catch (error) {
    console.error('检查登录状态失败', error);
    // 发生错误时，显示未登录状态
    page.setData({
      isLoggedIn: false,
      userInfo: null,
      openid: ''
    });
  }
};

export const handleLoginSuccess = (page: any, userInfo: UserInfo, openid: string) => {
  wx.removeStorageSync('userLoggedOut');

  page.setData({
    isLoggedIn: true,
    userInfo,
    openid,
    showLoginPopup: false
  });

  const app = getApp<IAppOption>();
  if (app.setGlobalLoginStatus) {
    app.setGlobalLoginStatus(true, userInfo, openid);
  }
};

// 用户主动点击登录按钮时的处理逻辑
export const handleUserLogin = async (page: any): Promise<void> => {
  try {
    // 获取用户openid
    const openid = await getOpenid();
    if (!openid) {
      wx.showToast({
        title: '获取用户信息失败',
        icon: 'none'
      });
      return;
    }

    // 检查云端是否有用户数据
    const cloudUserInfo = await getUserInfoFromCloud(openid);
    if (cloudUserInfo) {
      // 云端有数据，直接登录成功
      page.setData({
        isLoggedIn: true,
        userInfo: cloudUserInfo,
        openid,
        showLoginPopup: false
      });

      // 同步更新本地存储
      wx.setStorageSync('userInfo', cloudUserInfo);

      // 更新全局状态
      const app = getApp<IAppOption>();
      if (app.setGlobalLoginStatus) {
        app.setGlobalLoginStatus(true, cloudUserInfo, openid);
      }

      wx.showToast({
        title: '登录成功',
        icon: 'success'
      });

      // 如果页面有loadUserData方法，调用它
      if (page.loadUserData && typeof page.loadUserData === 'function') {
        await page.loadUserData();
      }
    } else {
      // 云端没有数据，显示登录弹窗让用户输入信息
      page.setData({
        showLoginPopup: true
      });
    }
  } catch (error) {
    console.error('登录检测失败', error);
    wx.showToast({
      title: '登录失败，请重试',
      icon: 'none'
    });
  }
};
