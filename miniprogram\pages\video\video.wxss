/* 视频播放页面样式 */
.container {
  height: 100vh;
  background-color: #000000;
  display: flex;
  flex-direction: column;
  position: relative;
}

/* 返回按钮 */
.back-btn {
  position: fixed;
  /* top 和 height 通过内联样式设置，与胶囊按钮保持一致 */
  left: 32rpx;
  width: 80rpx; /* 调大宽度，提升点击体验 */
  /* height 通过内联样式动态设置 */
  background-color: rgba(0, 0, 0, 0.5);
  border-radius: 40rpx; /* 调整圆角适应新宽度 */
  display: flex;
  align-items: center;
  justify-content: center;
  z-index: 1000;
  transition: all 0.2s ease;
}

.back-icon {
  color: #FFFFFF;
  font-size: 44rpx; /* 调大字体大小，适应更大的按钮 */
  font-weight: bold;
  margin-left: -6rpx; /* 调整视觉居中 */
}

.back-btn:active {
  background-color: rgba(0, 0, 0, 0.7);
  transform: scale(0.95);
}

/* 视频容器 */
.video-container {
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  display: flex;
  align-items: center;
  justify-content: center;
}

/* 视频播放器 */
.video-player {
  width: 100%;
  height: 100%;
  background-color: #000000;
}



/* 播放统计信息 */
.play-stats {
  position: fixed;
  bottom: 32rpx;
  left: 32rpx;
  right: 32rpx;
  background-color: rgba(255, 255, 255, 0.95);
  padding: 32rpx;
  border-radius: 16rpx;
  z-index: 9999; /* 提高层级，确保在全屏时也显示 */
  backdrop-filter: blur(10rpx);
  pointer-events: none; /* 防止遮挡视频控制按钮 */
}

.stat-item {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 24rpx;
}

.stat-label {
  font-size: 28rpx;
  color: #6B7280;
}

.stat-value {
  font-size: 32rpx;
  font-weight: bold;
  color: #92400E;
}

/* 打卡状态 */
.checkin-status {
  display: flex;
  align-items: center;
  justify-content: center;
  padding: 24rpx;
  border-radius: 12rpx;
  margin-top: 16rpx;
}

.checkin-status.success {
  background-color: #D1FAE5;
  color: #065F46;
}

.checkin-status.pending {
  background-color: #FEF3C7;
  color: #92400E;
  flex-direction: column;
}

.checkin-icon {
  font-size: 32rpx;
  font-weight: bold;
  margin-right: 16rpx;
}

.checkin-text {
  font-size: 28rpx;
  font-weight: 600;
}

.progress-text {
  font-size: 24rpx;
  margin-top: 8rpx;
  opacity: 0.8;
}
