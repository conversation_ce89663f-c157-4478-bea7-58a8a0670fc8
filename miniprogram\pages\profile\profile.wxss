/* pages/profile/profile.wxss */
.container {
  min-height: 100vh;
  background-color: #F8F6F2;
  padding-bottom: 40rpx;
}

.capsule-safe-area {
  width: 100%;
  flex-shrink: 0;
}

/* 自定义导航栏 */
.custom-navbar {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  background-color: #FFFFFF;
  z-index: 1000;
  border-bottom: 1rpx solid #F3F4F6;
}

.navbar-content {
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: 0 30rpx;
}

.navbar-left {
  width: 80rpx;
  height: 100%;
  display: flex;
  align-items: center;
  justify-content: flex-start;
}

.back-icon {
  font-size: 48rpx;
  color: #1F2937;
  font-weight: bold;
  line-height: 1;
}

.navbar-title {
  font-size: 36rpx;
  font-weight: bold;
  color: #1F2937;
  flex: 1;
  text-align: center;
}

.navbar-right {
  width: 80rpx;
}

/* 个人资料表单 */
.profile-form {
  background-color: #FFFFFF;
  margin: 0 30rpx;
  border-radius: 16rpx;
  overflow: hidden;
}

.form-item {
  display: flex;
  align-items: center;
  padding: 30rpx;
  border-bottom: 1rpx solid #F3F4F6;
}

.form-item:last-child {
  border-bottom: none;
}

.form-label {
  width: 160rpx;
  font-size: 28rpx;
  color: #4B5563;
  flex-shrink: 0;
}

.required {
  color: #EF4444;
}

.form-content {
  flex: 1;
  margin-left: 20rpx;
}

/* 头像样式 */
.avatar-wrapper {
  background: transparent;
  padding: 0;
  margin: 0;
  width: auto;
  height: auto;
  border: none;
  outline: none;
}

.avatar-wrapper::after {
  border: none;
}

.avatar {
  width: 120rpx;
  height: 120rpx;
  border-radius: 60rpx;
  border: 4rpx solid #F3F4F6;
  display: block;
}

/* 输入框样式 */
.form-input {
  width: 100%;
  height: 60rpx;
  border: 1rpx solid #E5E7EB;
  border-radius: 8rpx;
  padding: 0 20rpx;
  box-sizing: border-box;
  font-size: 28rpx;
  color: #1F2937;
  background-color: #FFFFFF;
}

.form-input:focus {
  border-color: #92400E;
}

/* 文本显示 */
.form-text {
  font-size: 28rpx;
  color: #6B7280;
  padding: 15rpx 0;
}

/* 选择器样式 */
.form-picker {
  height: 60rpx;
  line-height: 60rpx;
  border: 1rpx solid #E5E7EB;
  border-radius: 8rpx;
  padding: 0 20rpx;
  font-size: 28rpx;
  color: #1F2937;
  background-color: #FFFFFF;
  position: relative;
}

.form-picker::after {
  content: '';
  position: absolute;
  right: 20rpx;
  top: 50%;
  transform: translateY(-50%);
  width: 0;
  height: 0;
  border-left: 8rpx solid transparent;
  border-right: 8rpx solid transparent;
  border-top: 8rpx solid #9CA3AF;
}

/* 操作按钮 */
.action-buttons {
  padding: 40rpx 30rpx;
  display: flex;
  flex-direction: column;
  gap: 20rpx;
}

.btn {
  width: 100%;
  height: 80rpx;
  border-radius: 8rpx;
  font-size: 28rpx;
  display: flex;
  align-items: center;
  justify-content: center;
  border: none;
}

.btn::after {
  border: none;
}

.btn-primary {
  background-color: #92400E;
  color: #FFFFFF;
}

.btn-secondary {
  background-color: #F3F4F6;
  color: #92400E;
}

.btn[disabled] {
  opacity: 0.6;
  cursor: not-allowed;
}

.btn-primary[disabled] {
  background-color: #9CA3AF;
}
